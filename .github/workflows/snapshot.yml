name: Snapshot Release

on:
  push:
    branches:
      - develop

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  snapshot:
    name: Snapshot Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'pnpm'
          registry-url: 'https://registry.npmjs.org/'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run tests
        run: pnpm run test:ci

      - name: Release snapshot version
        run: |
          # 确保在正确的分支
          git checkout develop
          
          # 获取当前时间戳和短commit hash
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          SHORT_COMMIT=$(git rev-parse --short HEAD)
          
          # 读取当前版本，移除任何现有的snapshot标识
          CURRENT_VERSION=$(node -p "require('./package.json').version.split('-')[0]")
          
          # 生成唯一的snapshot版本号：base-snapshot.timestamp.commit
          SNAPSHOT_VERSION="${CURRENT_VERSION}-snapshot.${TIMESTAMP}.${SHORT_COMMIT}"
          
          echo "生成snapshot版本号: $SNAPSHOT_VERSION"
          
          # 直接设置版本号
          npm version $SNAPSHOT_VERSION --no-git-tag-version
          
          # 使用pnpm发布snapshot版本（与DPML项目保持一致）
          pnpm publish --tag snapshot --no-git-checks
          
          # 输出版本信息供后续步骤使用
          echo "SNAPSHOT_VERSION=$SNAPSHOT_VERSION" >> $GITHUB_ENV
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.ORG_NPM_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.ORG_NPM_TOKEN }}

      - name: Comment on related PRs
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            const { execSync } = require('child_process');
            
            // 获取snapshot版本号
            const version = process.env.SNAPSHOT_VERSION;
            
            // 查找相关的PR
            const { data: prs } = await github.rest.pulls.list({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              base: 'develop'
            });
            
            const comment = `🚀 **Snapshot版本已发布!**
            
            📦 版本号: \`${version}\`
            🔗 安装命令: \`npx dpml-prompt@${version} <command>\`
            或者: \`npx dpml-prompt@snapshot <command>\`
            
            📚 使用示例:
            \`\`\`bash
            npx dpml-prompt@${version} hello
            npx dpml-prompt@${version} init
            npx dpml-prompt@${version} action <roleId>
            \`\`\`
            
            💡 你可以使用这个snapshot版本测试最新的develop分支功能。`;
            
            // 为每个相关PR添加评论
            for (const pr of prs) {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: pr.number,
                body: comment
              });
            } 