name: Beta Release

on:
  push:
    branches:
      - staging
  workflow_dispatch:

concurrency: ${{ github.workflow }}-${{ github.ref }}

jobs:
  beta:
    name: Beta Release
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: 10

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'pnpm'
          registry-url: 'https://registry.npmjs.org/'

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Run tests
        run: pnpm run test:ci

      - name: Release beta version
        run: |
          # 确保在正确的分支
          git checkout staging
          
          # 读取当前版本，移除任何现有的预发布标识
          CURRENT_VERSION=$(node -p "require('./package.json').version.split('-')[0]")
          
          # 检查是否已存在beta版本，如果存在则递增
          EXISTING_BETA=$(npm view dpml-prompt@beta version 2>/dev/null || echo "")
          
          if [[ $EXISTING_BETA == $CURRENT_VERSION-beta.* ]]; then
            # 提取现有beta版本号并递增
            BETA_NUMBER=$(echo $EXISTING_BETA | sed "s/$CURRENT_VERSION-beta\.//")
            NEXT_BETA_NUMBER=$((BETA_NUMBER + 1))
          else
            # 首个beta版本
            NEXT_BETA_NUMBER=1
          fi
          
          # 生成beta版本号：base-beta.number
          BETA_VERSION="${CURRENT_VERSION}-beta.${NEXT_BETA_NUMBER}"
          
          echo "生成beta版本号: $BETA_VERSION"
          
          # 直接设置版本号
          npm version $BETA_VERSION --no-git-tag-version
          
          # 使用pnpm发布beta版本
          pnpm publish --tag beta --no-git-checks
          
          # 输出版本信息供后续步骤使用
          echo "BETA_VERSION=$BETA_VERSION" >> $GITHUB_ENV
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NPM_TOKEN: ${{ secrets.ORG_NPM_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.ORG_NPM_TOKEN }}

      - name: Comment on related PRs
        if: success()
        uses: actions/github-script@v7
        with:
          script: |
            const { execSync } = require('child_process');
            
            // 获取beta版本号
            const version = process.env.BETA_VERSION;
            
            // 查找相关的PR (staging相关的PR)
            const { data: prs } = await github.rest.pulls.list({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open'
            });
            
            // 过滤staging相关的PR
            const stagingPrs = prs.filter(pr => 
              pr.base.ref === 'staging' || pr.head.ref === 'staging'
            );
            
            const comment = `🎯 **Beta版本已发布!**
            
            📦 版本号: \`${version}\`
            🔗 安装命令: \`npx dpml-prompt@${version} <command>\`
            或者: \`npx dpml-prompt@beta <command>\`
            
            📚 使用示例:
            \`\`\`bash
            npx dpml-prompt@${version} hello
            npx dpml-prompt@${version} init
            npx dpml-prompt@${version} action <roleId>
            \`\`\`
            
            💡 这是一个稳定的预览版本，适合用户测试和反馈。
            
            🔄 **推荐迁移路径**: 从 \`@snapshot\` → \`@beta\` 获得更稳定的体验。`;
            
            // 为相关PR添加评论
            for (const pr of stagingPrs) {
              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: pr.number,
                body: comment
              });
            }
            
            // 如果没有staging相关PR，在最近的develop PR中也通知
            if (stagingPrs.length === 0) {
              const developPrs = prs.filter(pr => pr.base.ref === 'develop').slice(0, 3);
              for (const pr of developPrs) {
                await github.rest.issues.createComment({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: pr.number,
                  body: comment
                });
              }
            }