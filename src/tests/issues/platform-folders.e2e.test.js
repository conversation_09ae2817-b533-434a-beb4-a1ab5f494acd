const { execSync, spawn } = require('child_process')
const path = require('path')
const fs = require('fs-extra')
const os = require('os')

describe('Platform-Folders 兼容性问题 - E2E Tests', () => {
  let tempDir
  let originalPlatform

  beforeAll(async () => {
    // 创建临时目录用于测试
    tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'promptx-platform-test-'))
    originalPlatform = process.platform
  })

  afterAll(async () => {
    if (tempDir) {
      await fs.remove(tempDir)
    }
    // 恢复原始平台
    Object.defineProperty(process, 'platform', {
      value: originalPlatform
    })
  })

  /**
   * 模拟Windows环境
   */
  function mockWindowsEnvironment() {
    // 模拟Windows平台
    Object.defineProperty(process, 'platform', {
      value: 'win32',
      configurable: true
    })

    // 模拟Windows环境变量
    const originalEnv = { ...process.env }
    process.env.APPDATA = 'C:\\Users\\<USER>\\AppData\\Roaming'
    process.env.LOCALAPPDATA = 'C:\\Users\\<USER>\\AppData\\Local'
    process.env.USERPROFILE = 'C:\\Users\\<USER>