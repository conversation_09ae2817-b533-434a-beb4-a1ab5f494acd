/**
 * 锦囊命令导出
 */

const InitCommand = require('./InitCommand')
const HelloCommand = require('./HelloCommand')
const ActionCommand = require('./ActionCommand')
const LearnCommand = require('./LearnCommand')
const RecallCommand = require('./RecallCommand')
const RememberCommand = require('./RememberCommand')

module.exports = {
  InitCommand,
  HelloCommand,
  ActionCommand,
  LearnCommand,
  RecallCommand,
  RememberCommand
}
