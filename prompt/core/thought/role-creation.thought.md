<thought>
  <exploration>
    ## 领域快速识别
    
    ### 从用户描述中提取核心信息
    - **领域关键词**：用户提到的技术栈、职业、业务领域
    - **功能期望**：用户希望AI助手具备的核心能力
    - **应用场景**：主要的使用场景和工作环境
    
    ### 领域标准化映射
    - **技术领域**：前端开发、后端开发、移动开发、数据分析等
    - **业务领域**：产品管理、市场营销、设计创意、运营管理等
    - **综合领域**：项目管理、技术架构、创业咨询、教育培训等
    
    ### 快速能力框架识别
    - 该领域的核心技能需求
    - 该领域的典型工作流程
    - 该领域的专业知识体系
  </exploration>
  
  <reasoning>
    ## 基于ResourceManager的资源生成逻辑
    
    ### 架构驱动的生成策略
    ```
    用户描述 → 领域识别 → 资源规划 → 文件生成 → ResourceManager发现
    ```
    
    ### 镜像结构思维模式
    - **结构一致性**：用户资源目录镜像系统`prompt/domain/`结构
    - **认知负载最小化**：与系统结构保持一致，降低学习成本
    - **资源聚合原则**：角色相关的所有文件统一管理在角色目录下
    
    ### 三组件标准化填充策略
    - **Personality设计**：
      - 基于领域的通用思维特征
      - 该领域专业人士的认知偏好
      - 高效协作的交互风格
    
    - **Principle设计**：
      - 该领域的标准工作流程
      - 通用的质量标准和最佳实践
      - 常见问题的处理原则
    
    - **Knowledge设计**：
      - 该领域的核心技能栈
      - 必备的专业知识体系
      - 常用工具和方法论
    
    ### 文件组织优化思维
    - **目录结构规划**：`.promptx/resource/domain/{roleId}/`
    - **扩展文件支持**：thought/、execution/子目录按需创建
    - **引用关系设计**：优先使用@!引用机制，实现模块化
    - **发现机制适配**：确保ResourceManager能正确发现和加载
    
    ### 质量保证机制
    - 确保三组件逻辑一致
    - 验证角色定位清晰准确
    - 保证实用性和可操作性
    - 符合DPML协议规范
    - 满足ResourceManager发现要求
  </reasoning>
</thought> 