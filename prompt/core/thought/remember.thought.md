<thought>
  <exploration>
    ## 记忆价值探索
    
    ### 什么值得记忆？
    - **用户身份**：职业、角色、专业背景
    - **工作偏好**：习惯、风格、决策模式
    - **项目信息**：当前工作、重要节点、团队
    - **经验教训**：成功案例、失败原因、解决方案
    - **重要关系**：关键联系人、合作方式
    
    ### 记忆触发信号
    - 用户明确说"记住"
    - 重复提及的信息
    - 重要决策和选择
    - 问题解决的关键步骤
    - 用户反馈和评价
  </exploration>
  
  <reasoning>
    ## 评估推理逻辑
    
    ### 三维度快速评估
    - **重要性**：对用户有多重要？(核心身份>工作相关>一般信息>无关内容)
    - **可信度**：信息有多可靠？(用户陈述>逻辑推导>第三方>推测)
    - **持久性**：能用多长时间？(长期有效>中期有效>短期有效>即时信息)
    
    ### 简单决策规则
    - **显式要求** → 直接记忆
    - **三个维度都高** → 推荐记忆
    - **重要性高 + 其他中等** → 考虑记忆
    - **重要性低** → 不记忆
    
    ### 特殊情况处理
    - **信息冲突** → 选择更可信、更新的
    - **信息更新** → 替换旧信息
    - **信息补充** → 关联到现有记忆
  </reasoning>
  
  <challenge>
    ## 关键质疑
    
    ### 评估是否过于主观？
    - AI的判断标准是否一致？
    - 不同用户类型是否需要不同标准？
    - 如何处理边界情况？
    
    ### 是否会遗漏重要信息？
    - 看似不重要但长期有价值的信息？
    - 用户未明确表达但暗示重要的信息？
    - 情境变化导致价值变化的信息？
  </challenge>
  
  <plan>
    ## 思考结构
    
    ### 评估思路
    1. 识别信息类型和来源
    2. 快速三维度评估
    3. 应用决策规则
    4. 考虑特殊情况
    5. 形成记忆建议
  </plan>
</thought> 