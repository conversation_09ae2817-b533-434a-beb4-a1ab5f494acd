<thought>
  <exploration>
    ## 回忆需求探索
    
    ### 什么时候需要回忆？
    - **明确查询**：用户直接问"你还记得..."
    - **上下文缺失**：当前对话需要历史信息支持
    - **模式识别**：发现与过往经验的相似性
    - **决策支持**：需要参考历史决策和结果
    - **个性化服务**：根据用户偏好提供定制建议
    
    ### 回忆的信息类型
    - **身份信息**：用户的角色、职业、背景
    - **偏好设置**：工作习惯、沟通风格、决策偏好  
    - **项目历史**：过往项目、团队、关键节点
    - **问题解决**：成功案例、失败教训、解决方案
    - **关系网络**：重要联系人、合作模式
    
    ### 回忆触发信号
    - 用户提及过往事件
    - 当前问题与历史相似
    - 需要个性化推荐
    - 决策需要历史依据
    - 用户询问"你知道我..."
  </exploration>
  
  <reasoning>
    ## 回忆检索逻辑
    
    ### 三层检索策略
    - **关键词匹配**：直接匹配用户查询的关键词
    - **语义相关**：理解查询意图，找到相关概念
    - **时空关联**：考虑时间、项目、情境的关联性
    
    ### 相关性评估
    - **直接相关**：完全匹配查询内容
    - **间接相关**：与查询主题相关联
    - **背景相关**：提供上下文支持
    - **无关信息**：与当前需求不匹配
    
    ### 结果组织原则
    - **按相关性排序**：最相关的优先展示
    - **按时间排序**：最新或最相关时期的优先
    - **按重要性排序**：对用户最重要的优先
    - **分类呈现**：按信息类型分组展示
    
    ### 回忆失败处理
    - **无匹配结果** → 告知用户并询问更多信息
    - **模糊匹配** → 提供近似结果并确认
    - **过多结果** → 筛选最相关的并询问具体需求
  </reasoning>
  
  <challenge>
    ## 关键质疑
    
    ### 检索准确性问题
    - 如何避免误匹配不相关的记忆？
    - 语义理解是否足够准确？
    - 时间久远的记忆是否还有价值？
    
    ### 隐私和安全考虑
    - 是否会意外泄露敏感信息？
    - 如何处理用户已经遗忘想隐藏的信息？
    - 记忆的访问权限如何控制？
    
    ### 用户体验挑战
    - 回忆过程是否会打断对话流程？
    - 如何平衡信息完整性和简洁性？
    - 用户如何纠正错误的回忆结果？
    
    ### 系统性能问题
    - 大量记忆的检索速度如何保证？
    - 复杂查询的计算成本是否过高？
    - 如何处理记忆存储的增长？
  </challenge>
  
  <plan>
    ## 思考结构
    
    ### 检索思路
    1. 分析查询意图和类型
    2. 应用三层检索策略
    3. 评估结果相关性
    4. 组织和排序信息
    5. 形成回忆结果
  </plan>
</thought> 