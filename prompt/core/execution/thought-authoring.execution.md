<execution>
  <constraint>
    ## 客观技术限制
    - **DPML语法约束**：必须遵循EBNF定义的thought语法结构
    - **XML格式要求**：标签必须正确闭合，属性值必须用双引号包围
    - **Markdown兼容性**：内容部分必须是有效的Markdown格式，支持Mermaid图表
    - **文件编码**：必须使用UTF-8编码
    - **思维模式固化**：四种思维模式的语义特征不可改变
    - **可视化限制**：Mermaid图表必须符合语法规范
  </constraint>

  <rule>
    ## 强制性编写规则
    - **纯XML结构**：thought文件必须从`<thought>`标签开始，不得包含任何XML结构外的内容
    - **根标签强制**：文件必须使用`<thought>`作为根标签包装全部内容
    - **子标签命名**：只能使用规范定义的四个思维模式子标签：exploration, reasoning, plan, challenge
    - **语义一致性**：每个子标签内容必须与其思维模式语义定义保持一致
    - **文件纯净性**：除了`<thought>`标签结构外，不得包含任何其他内容
    - **内容实质性**：包含的子标签都必须有实质性思考内容，不得为空
  </rule>

  <guideline>
    ## 编写指导原则
    - **思维模式清晰性**：每个子标签的内容应明确体现对应的思维特征
    - **推荐思考顺序**：建议按exploration → challenge → reasoning → plan顺序组织思考
    - **可视化优先**：优先使用Mermaid图表表达复杂的思维关系和逻辑结构
    - **内容层次化**：使用Markdown的标题、列表等结构组织思考内容
    - **思维完整性**：确保思考覆盖问题的关键维度，避免思维盲区
    - **灵活组合**：根据实际需求选择合适的思维模式组合，无需全部使用
  </guideline>

  <process>
    ## 编写执行流程
    
    ### Phase 1: 思考需求分析
    1. **明确思考目标**：确定这个thought要解决什么问题或分析什么议题
    2. **识别思考复杂度**：判断问题是否需要多维度思考
    3. **选择思维模式**：根据问题特点选择合适的思维模式组合
    4. **确定思考深度**：决定每个思维模式需要的分析深度

    ### Phase 2: 思维模式规划
    1. **探索思维规划**：
       - 确定需要发散思考的维度
       - 列出要探索的可能性和创新点
       - 规划关联性分析的范围

    2. **挑战思维规划**：
       - 识别需要质疑的假设和观点
       - 列出潜在风险和问题点
       - 规划批判性分析的角度

    3. **推理思维规划**：
       - 确定需要验证的逻辑链条
       - 规划因果关系分析路径
       - 设计系统性推理结构

    4. **计划思维规划**：
       - 明确需要设计的行动方案
       - 规划决策路径和组织结构
       - 确定系统架构的层次

    ### Phase 3: DPML结构实现
    
    **关键要求：文件必须从`<thought>`标签直接开始**
    
    ```xml
    <thought>
      <exploration>
        # 探索思维：发散性思考
        [跳跃思考、生成可能性、寻找创新点和关联性]
      </exploration>
      
      <challenge>
        # 挑战思维：批判性思考
        [逆向思考、质疑假设、识别风险和问题点]
      </challenge>
      
      <reasoning>
        # 推理思维：系统性思考
        [连续推理、验证逻辑、分析因果关系]
      </reasoning>
      
      <plan>
        # 计划思维：结构性思考
        [设计方案、决策路径、组织架构]
      </plan>
    </thought>
    ```
    
    **推荐思考顺序示例：**
    ```xml
    <thought>
      <!-- 1. 先发散探索 -->
      <exploration>
        ## 可能性探索
        - 方向A：...
        - 方向B：...
        
        ## 关联性分析
        ```mermaid
        mindmap
          root)问题核心(
            分支1
            分支2
            分支3
        ```
      </exploration>
      
      <!-- 2. 再批判质疑 -->
      <challenge>
        ## 假设质疑
        - 对方向A的质疑：...
        - 对方向B的质疑：...
        
        ## 风险识别
        - 潜在风险1：...
        - 潜在风险2：...
      </challenge>
      
      <!-- 3. 然后系统推理 -->
      <reasoning>
        ## 逻辑验证
        ```mermaid
        flowchart TD
          A[前提] --> B[推理]
          B --> C[结论]
        ```
      </reasoning>
      
      <!-- 4. 最后制定计划 -->
      <plan>
        ## 行动方案
        1. 步骤一：...
        2. 步骤二：...
      </plan>
    </thought>
    ```

    ### Phase 4: 思维质量检查
    1. **思维模式验证**：确保每个子标签体现正确的思维特征
    2. **逻辑一致性检查**：验证不同思维模式间的逻辑关系
    3. **思考完整性审核**：确认思考覆盖问题的关键维度
    4. **可视化检查**：验证Mermaid图表语法正确性和表达清晰性
    5. **纯净性检查**：确认文件从`<thought>`标签开始，无多余内容
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 格式合规性
    - ✅ 文件从`<thought>`标签直接开始，无额外内容
    - ✅ 使用正确的DPML thought标签结构
    - ✅ 子标签命名符合四种思维模式规范
    - ✅ XML语法正确，标签正确闭合
    - ✅ Markdown格式规范，Mermaid图表有效

    ### 思维模式准确性
    - ✅ exploration体现发散性、跳跃性思考特征
    - ✅ challenge体现批判性、逆向思考特征
    - ✅ reasoning体现系统性、连续性推理特征
    - ✅ plan体现结构性、秩序性思考特征
    - ✅ 各思维模式语义边界清晰，不混淆

    ### 思考质量
    - ✅ 思考内容具有实质性和深度
    - ✅ 逻辑关系清晰，推理链条完整
    - ✅ 覆盖问题的关键维度，无明显盲区
    - ✅ 思维过程系统化，层次分明
    - ✅ 结论或方案具有可操作性

    ### 可视化效果
    - ✅ 恰当使用Mermaid图表表达复杂关系
    - ✅ 图表类型选择合适（mindmap, flowchart, graph等）
    - ✅ 可视化内容与文字描述相互补充
    - ✅ 图表简洁清晰，易于理解

    ### 文件纯净性
    - ✅ 文件结构完全符合DPML thought规范
    - ✅ 无任何XML结构外的多余内容
    - ✅ 体现thought文件的标准格式
    - ✅ 思维模式组合合理，符合实际需求
  </criteria>
</execution> 