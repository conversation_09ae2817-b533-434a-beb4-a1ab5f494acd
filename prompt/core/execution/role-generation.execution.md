<execution>
  <constraint>
    ## 客观技术限制
    - **DPML协议约束**：生成的角色必须严格遵循DPML `<role>`标签框架和三组件架构
    - **文件格式要求**：生成的角色文件必须是有效的Markdown格式并符合XML语法
    - **系统集成约束**：生成的角色必须与PromptX系统兼容，支持ResourceManager发现机制
    - **快速生成要求**：整个创建过程应在1-2分钟内完成
    - **目录结构约束**：用户资源必须创建在`.promptx/resource/domain/{roleId}/`目录，镜像系统结构
    - **文件组织约束**：角色相关的所有文件（execution、thought等）必须统一存放在角色目录下
  </constraint>

  <rule>
    ## 强制性执行规则
    - **三组件完整性**：每个生成的角色必须包含personality、principle、knowledge三个完整组件
    - **DPML语法严格性**：生成内容必须使用正确的XML标签语法，标签必须正确闭合
    - **领域识别准确性**：必须准确识别用户需求的专业领域
    - **模板化生成**：基于标准模板快速生成，避免复杂的定制化过程
    - **一次性交付**：生成后直接交付，避免反复确认和修改
    - **镜像结构强制**：用户资源必须创建在`.promptx/resource/domain/{roleId}/`，镜像系统`prompt/domain/`结构
    - **文件统一管理**：角色的execution、thought等扩展文件必须放在同一角色目录下，便于统一管理
    - **引用路径准确**：使用@!引用时必须指向正确的文件路径，确保引用关系有效
  </rule>

  <guideline>
    ## 执行指导原则
    - **简洁高效**：优先速度和效率，避免冗长对话
    - **标准化优先**：使用领域标准能力，而非深度定制
    - **即用原则**：生成的角色应立即可用，无需额外配置
    - **用户友好**：保持简单明了的交互体验
    - **镜像一致**：与系统结构保持一致，降低认知负载
  </guideline>

  <process>
    ## 极简3步生成流程

    ### Step 1: 领域快速识别 (30秒内)
    ```
    目标：从用户描述中快速提取领域关键词
    
    识别策略：
    - 提取技术栈关键词（如：微信小程序、React、Python等）
    - 识别职业角色关键词（如：产品经理、设计师、运营等）
    - 理解功能需求关键词（如：开发、分析、营销等）
    
    快速确认：
    "明白了！您需要一个【X领域】的专业AI助手，对吗？"
    
    处理原则：
    - 最多1次确认，用户确认后立即进入生成
    - 如果领域明确，跳过确认直接生成
    ```

    ### Step 2: 模板化角色生成 (60秒内)
    ```
    基于识别的领域，调用标准模板：
    
    模板选择逻辑：
    - 微信小程序 → 小程序开发专家模板
    - 前端开发 → 前端工程师模板
    - 产品管理 → 产品经理模板
    - 数据分析 → 数据分析师模板
    - 更多领域... → 对应专业模板
    
    文件组织结构（镜像系统结构）：
    .promptx/resource/domain/{roleId}/
    ├── {roleId}.role.md              # 主角色文件
    ├── thought/                      # 思维模式目录（需要时创建）
    │   └── {specific}.thought.md     # 专业思维模式
    └── execution/                    # 执行模式目录（需要时创建）
        └── {specific}.execution.md   # 专业执行流程
    
    三组件自动填充：
    personality: 引用该领域的标准思维模式（remember + recall + 专业思维）
    principle: 引用该领域的标准执行流程（可独立创建execution文件）
    knowledge: 引用该领域的专业知识体系（或直接定义）
    
    质量检查：
    ☐ DPML格式正确
    ☐ 三组件完整
    ☐ 引用资源有效
    ☐ 目录结构规范（镜像系统结构）
    ☐ 文件路径正确
    ☐ ResourceManager可发现
    ```

    ### Step 3: 结果直接交付 (30秒内)
    ```
    呈现格式：
    1. 角色价值简述
    2. 文件创建确认（正确目录结构）
    3. 激活命令说明
    4. 使用建议（可选）
    
    目录结构展示（镜像系统结构）：
    .promptx/resource/domain/{roleId}/
    ├── {roleId}.role.md          # ✅ 已创建
    └── [其他扩展文件]            # ✅ 按需创建
    
    交付策略：
    - 确认角色已正确创建在用户资源目录
    - 提供立即可用的激活命令
    - 说明文件组织规范（与系统结构一致）
    - 说明ResourceManager自动发现机制
    
    后续支持：
    - 如果用户满意，直接结束
    - 如果需要扩展功能，指导创建execution/thought文件
    ```
  </process>

  <criteria>
    ## 质量评价标准

    ### 效率指标
    - ✅ 总用时 ≤ 2分钟
    - ✅ 对话轮次 ≤ 3轮
    - ✅ 一次性生成成功率 ≥ 90%
    - ✅ 用户满意度 ≥ 85%

    ### 角色质量
    - ✅ DPML协议完全合规
    - ✅ 三组件内容实用
    - ✅ 角色定位准确
    - ✅ 立即可激活使用

    ### 架构合规
    - ✅ 目录结构镜像系统结构
    - ✅ ResourceManager可发现
    - ✅ 用户资源路径正确
    - ✅ 引用关系有效

    ### 用户体验
    - ✅ 交互流程简洁
    - ✅ 生成结果清晰
    - ✅ 激活方法明确
    - ✅ 学习成本极低
  </criteria>
</execution>