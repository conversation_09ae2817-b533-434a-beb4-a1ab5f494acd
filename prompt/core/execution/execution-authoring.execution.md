<execution>
  <constraint>
    ## 客观技术限制
    - **DPML语法约束**：必须遵循EBNF定义的execution语法结构
    - **XML格式要求**：标签必须正确闭合，属性值必须用双引号包围
    - **Markdown兼容性**：内容部分必须是有效的Markdown格式
    - **文件编码**：必须使用UTF-8编码
    - **优先级固化**：五个子标签的优先级关系不可改变
  </constraint>

  <rule>
    ## 强制性编写规则
    - **纯XML结构**：execution文件必须从`<execution>`标签开始，不得包含任何XML结构外的内容（如Markdown标题、注释等）
    - **根标签强制**：文件必须使用`<execution>`作为根标签包装全部内容
    - **子标签命名**：只能使用规范定义的五个子标签：constraint, rule, guideline, process, criteria
    - **优先级顺序**：子标签必须按constraint → rule → guideline → process → criteria顺序排列
    - **内容完整性**：每个子标签都必须包含实质性内容，不得为空
    - **语义一致性**：子标签内容必须与其语义定义保持一致
    - **文件纯净性**：除了`<execution>`标签结构外，不得包含任何其他内容
  </rule>

  <guideline>
    ## 编写指导原则
    - **语义明确性**：每个子标签的内容应清晰表达其特定语义
    - **内容层次化**：使用Markdown的标题、列表等结构组织内容
    - **实用性导向**：内容应具有实际操作指导价值
    - **简洁性原则**：避免冗长表述，保持核心要点突出
    - **一致性维护**：在整个文件中保持术语和表达方式的一致性
  </guideline>

  <process>
    ## 编写执行流程
    
    ### Phase 1: 分析需求和上下文
    1. **明确execution目的**：确定这个execution要解决什么问题
    2. **识别客观限制**：分析技术、环境、资源等客观约束条件
    3. **定义强制要求**：确定必须遵守的规则和底线要求
    4. **收集最佳实践**：整理相关领域的指导原则和建议

    ### Phase 2: 内容规划和结构设计
    1. **约束条件梳理**：
       - 列出所有客观存在的限制条件
       - 按重要性和影响程度排序
       - 确保约束条件的客观性和不可变性

    2. **规则定义设计**：
       - 识别必须严格遵守的行为准则
       - 明确违反规则的后果和风险
       - 确保规则与约束条件不冲突

    3. **指导原则制定**：
       - 提供灵活性建议和最佳实践
       - 允许根据具体情况调整
       - 确保不违反已定义的规则和约束

    4. **流程步骤设计**：
       - 在约束和规则框架内设计执行路径
       - 包含正常流程和异常处理
       - 确保步骤的可操作性和逻辑性

    5. **评价标准确立**：
       - 定义成功完成的判断依据
       - 考虑所有优先级更高元素的要求
       - 提供可量化的评估方法

    ### Phase 3: DPML结构实现
    
    **关键要求：文件必须从`<execution>`标签直接开始**
    
    ```xml
    <execution>
      <constraint>
        [客观限制条件内容]
      </constraint>
      
      <rule>
        [强制性规则内容]
      </rule>
      
      <guideline>
        [指导原则内容]
      </guideline>
      
      <process>
        [具体执行步骤]
      </process>
      
      <criteria>
        [评价标准内容]
      </criteria>
    </execution>
    ```
    
    **错误示例（禁止）：**
    ```markdown
    # 标题
    这是描述内容...
    
    <execution>
      ...
    </execution>
    ```

    ### Phase 4: 质量检查和优化
    1. **语法验证**：确保DPML语法正确性
    2. **语义一致性检查**：验证各部分逻辑关系
    3. **优先级关系验证**：确认无冲突和矛盾
    4. **实用性测试**：验证内容的可操作性
    5. **完整性审核**：确保覆盖所有必要方面
    6. **纯净性检查**：确认文件从`<execution>`标签开始，无多余内容
  </process>

  <criteria>
    ## 质量评价标准
    
    ### 格式合规性
    - ✅ 文件从`<execution>`标签直接开始，无额外内容
    - ✅ 使用正确的DPML execution标签结构
    - ✅ 五个子标签按规定顺序排列
    - ✅ XML语法正确，标签正确闭合
    - ✅ Markdown格式规范，层次清晰

    ### 内容完整性
    - ✅ 每个子标签都包含实质性内容
    - ✅ 约束条件体现客观性和不可变性
    - ✅ 规则体现强制性和明确性
    - ✅ 指导原则体现建议性和灵活性
    - ✅ 流程步骤具有可操作性和逻辑性
    - ✅ 评价标准具有可验证性

    ### 语义一致性
    - ✅ 各子标签内容与其语义定义匹配
    - ✅ 优先级关系得到正确体现
    - ✅ 不存在逻辑冲突和矛盾
    - ✅ 术语使用保持一致性

    ### 实用价值
    - ✅ 内容具有实际指导意义
    - ✅ 步骤和标准可以实际执行
    - ✅ 能够解决实际问题
    - ✅ 适用于目标场景和用户
    
    ### 文件纯净性
    - ✅ 文件结构完全符合DPML execution规范
    - ✅ 无任何XML结构外的多余内容
    - ✅ 体现execution文件的标准格式
  </criteria>
</execution> 