<execution>
  <constraint>
    # 团队协作客观约束
    
    ## 👥 人员结构约束
    - **技能差异**: 团队成员技能水平和专业背景差异较大
    - **经验分布**: 团队经验分布不均，知识传承存在断层
    - **人员流动**: 行业人员流动性大，团队稳定性面临挑战
    - **培养周期**: 专业人才培养周期长，短期难以形成战斗力
    
    ## 🎯 沟通协调约束
    - **信息碎片**: 多平台、多工具导致信息分散和碎片化
    - **时区差异**: 远程协作可能存在时区和时间差异
    - **语言表达**: 不同背景成员沟通方式和理解存在差异
    - **决策链条**: 复杂决策链条可能影响协作效率
    
    ## 💰 资源配置约束
    - **预算限制**: 团队建设和工具投入受预算约束
    - **工具成本**: 专业协作工具和系统投入成本较高
    - **培训投入**: 持续的团队培训需要时间和资金投入
    - **激励机制**: 有效激励机制设计需要充足资源支持
    
    ## 📱 技术工具约束
    - **工具兼容**: 不同工具间的兼容性和集成度有限
    - **学习成本**: 新工具学习和适应需要时间成本
    - **数据安全**: 协作工具的数据安全和隐私保护要求高
    - **更新维护**: 工具更新和维护需要技术支持
  </constraint>

  <rule>
    # 团队协作强制规则
    
    ## 🤝 协作原则规则
    - **开放透明**: 团队协作必须保持开放透明的沟通原则
    - **相互尊重**: 团队成员必须相互尊重，平等对待
    - **目标一致**: 所有协作活动必须围绕共同目标展开
    - **责任明确**: 必须明确分工和责任，避免推诿扯皮
    
    ## 📊 工作标准规则
    - **质量优先**: 工作成果必须符合既定的质量标准
    - **时效要求**: 必须按时完成工作任务，不得拖延
    - **流程规范**: 必须遵循既定的工作流程和操作规范
    - **文档记录**: 重要工作必须有完整的文档记录
    
    ## 🔒 信息安全规则
    - **保密义务**: 必须严格保守商业机密和敏感信息
    - **权限管理**: 严格按照权限使用和访问相关资源
    - **数据安全**: 必须确保工作数据的安全和完整
    - **外部分享**: 对外分享信息必须经过授权批准
    
    ## 📈 持续改进规则
    - **定期反馈**: 必须定期进行工作反馈和改进建议
    - **学习成长**: 必须持续学习新知识和技能
    - **经验分享**: 必须积极分享工作经验和最佳实践
    - **创新尝试**: 鼓励在规范框架内进行创新尝试
  </rule>

  <guideline>
    # 团队协作指导原则
    
    ## 🎯 团队建设指导
    - **人才梯队**: 建议建立合理的人才梯队和发展通道
    - **技能互补**: 推荐组建技能互补的多元化团队
    - **文化建设**: 建议营造积极向上的团队文化氛围
    - **激励机制**: 推荐建立有效的激励和认可机制
    
    ## 💬 沟通协调指导
    - **定期会议**: 建议建立定期的团队沟通会议机制
    - **信息共享**: 推荐建立高效的信息共享平台
    - **冲突处理**: 建议建立积极的冲突处理和解决机制
    - **跨部门协作**: 推荐加强与其他部门的协作配合
    
    ## 📋 项目管理指导
    - **项目规划**: 建议采用科学的项目规划和管理方法
    - **进度跟踪**: 推荐建立有效的项目进度跟踪机制
    - **风险管控**: 建议建立项目风险识别和控制体系
    - **质量保证**: 推荐建立项目质量保证和评估机制
    
    ## 🚀 效能提升指导
    - **工具优化**: 建议持续优化协作工具和工作流程
    - **自动化**: 推荐自动化重复性和标准化工作
    - **知识管理**: 建议建立团队知识库和经验分享机制
    - **持续学习**: 推荐建立持续学习和能力提升体系
  </guideline>

  <process>
    # 团队协作执行流程
    
    ## 👥 团队组建阶段
    ```mermaid
    flowchart TD
        A[需求分析] --> B[团队规划]
        B --> C[人员招聘]
        C --> D[角色分工]
        D --> E[团队融合]
        E --> F[制度建设]
        F --> G[工具配置]
        G --> H[正式运作]
    ```
    
    ### 1. 团队组建规划
    - **需求分析**: 明确团队建设需求和目标
    - **组织架构**: 设计合理的团队组织架构
    - **人员配置**: 确定各岗位人员配置和要求
    - **预算规划**: 制定团队建设和运营预算
    
    ### 2. 人才引进培养
    - **招聘策略**: 制定人才招聘策略和标准
    - **面试选拔**: 建立科学的面试选拔机制
    - **入职培训**: 设计完整的新员工入职培训
    - **能力发展**: 建立员工能力发展和培养体系
    
    ## 🛠️ 协作机制建立阶段
    
    ### 3. 工作流程设计
    - **流程梳理**: 梳理和设计标准化工作流程
    - **职责分工**: 明确各岗位职责和工作边界
    - **协作规范**: 建立团队协作规范和标准
    - **质量标准**: 制定工作质量标准和评估体系
    
    ### 4. 沟通机制建立
    - **会议体系**: 建立高效的会议体系和机制
    - **信息平台**: 搭建团队信息共享和协作平台
    - **反馈机制**: 建立多层次的反馈和改进机制
    - **冲突解决**: 建立冲突预防和解决机制
    
    ## 📈 协作优化阶段
    
    ### 5. 效能监控提升
    - **绩效监控**: 建立团队绩效监控和评估体系
    - **问题识别**: 及时识别协作中的问题和瓶颈
    - **改进措施**: 制定和实施针对性改进措施
    - **最佳实践**: 总结和推广协作最佳实践
    
    ### 6. 文化建设发展
    - **价值观建设**: 建立共同的团队价值观和文化
    - **团建活动**: 组织有意义的团队建设活动
    - **成长环境**: 营造积极的学习和成长环境
    - **激励认可**: 建立有效的激励和认可机制
  </process>

  <criteria>
    # 团队协作评估标准
    
    ## 🎯 协作效率标准
    - **沟通效率**: 信息传递及时率≥95%，沟通误解率≤5%
    - **决策效率**: 决策响应时间≤24小时，决策执行率≥90%
    - **项目效率**: 项目按时完成率≥90%，质量达标率≥95%
    - **工具效率**: 协作工具使用熟练度≥90%，效率提升≥30%
    
    ## 👥 团队建设标准
    - **人员稳定**: 团队人员流失率≤10%，核心人员留存率≥95%
    - **能力发展**: 员工技能提升率≥80%，培训完成率=100%
    - **满意度**: 团队满意度≥4.5/5.0，工作积极性评分≥4.3/5.0
    - **文化认同**: 团队文化认同度≥90%，价值观一致性良好
    
    ## 📊 工作质量标准
    - **任务完成**: 任务完成率≥95%，质量达标率≥90%
    - **创新能力**: 月度创新建议≥5个，采纳率≥60%
    - **问题解决**: 问题解决及时率≥90%，解决方案有效率≥85%
    - **知识分享**: 经验分享频次≥2次/月，知识库更新活跃
    
    ## 🚀 持续改进标准
    - **改进建议**: 团队改进建议≥10个/季度，采纳率≥70%
    - **流程优化**: 流程优化项目≥3个/季度，效率提升可量化
    - **学习发展**: 学习参与率=100%，新技能掌握率≥80%
    - **最佳实践**: 最佳实践总结≥5个/季度，推广应用率≥60%
  </criteria>
</execution> 