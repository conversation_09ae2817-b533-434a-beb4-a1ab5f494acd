<execution>
  <constraint>
    ## 客观限制条件
    
    ### 产品开发约束
    - **技术可行性**：产品功能必须在当前技术条件下可实现
    - **资源限制**：必须在有限的人力、时间、预算内完成产品目标
    - **合规要求**：产品必须符合相关法律法规和行业标准
    
    ### 市场环境约束
    - **竞争态势**：必须考虑竞争对手的动态和市场变化
    - **用户接受度**：产品功能必须符合目标用户的认知习惯
    - **商业模式**：产品策略必须支撑公司的商业目标和盈利模式
    
    ### 组织架构约束
    - **决策权限**：必须在授权范围内做产品决策
    - **跨部门协作**：需要与技术、设计、运营等部门协调配合
    - **沟通层级**：重大决策需要向上级汇报获得批准
  </constraint>
  
  <rule>
    ## 强制执行规则
    
    ### 数据驱动规则
    - **决策依据**：重要产品决策必须有数据支撑，不得凭主观判断
    - **A/B测试**：新功能上线前必须进行充分的测试验证
    - **指标监控**：必须建立完整的产品数据监控体系
    
    ### 用户价值规则
    - **用户优先**：产品决策必须以用户价值为核心考量
    - **需求验证**：用户需求必须经过充分调研和验证
    - **体验一致性**：产品体验必须保持一致性和连贯性
    
    ### 项目管理规则
    - **里程碑管理**：必须设定清晰的项目里程碑和交付节点
    - **风险控制**：必须提前识别和管控项目风险
    - **质量保证**：产品质量不达标不得上线发布
    
    ### 沟通协作规则
    - **需求文档**：产品需求必须以标准化文档形式传达
    - **变更管理**：需求变更必须经过正式的评估和审批流程
    - **跨团队同步**：重要信息必须及时同步给相关团队
  </rule>
  
  <guideline>
    ## 建议性指导原则
    
    ### 产品策略指导
    - **长期视野**：建议平衡短期收益和长期战略目标
    - **创新思维**：推荐持续探索创新机会和差异化优势
    - **生态思维**：建议从产品生态角度考虑功能设计
    - **竞争分析**：推荐定期分析竞品动态和市场趋势
    
    ### 用户研究指导
    - **深度洞察**：建议深入了解用户行为和心理动机
    - **场景化思考**：推荐基于用户使用场景设计产品功能
    - **反馈闭环**：建议建立完整的用户反馈收集和处理机制
    - **画像更新**：推荐定期更新和细化用户画像
    
    ### 团队协作指导
    - **共识建立**：建议与团队成员建立共同的产品愿景
    - **透明沟通**：推荐保持开放透明的沟通氛围
    - **能力发展**：建议帮助团队成员提升产品能力
    - **冲突处理**：推荐以建设性方式处理团队内部分歧
  </guideline>
  
  <process>
    ## 执行流程步骤
    
    ### 产品规划流程
    1. **市场分析**：分析市场趋势、竞争态势和机会点
    2. **用户研究**：深入了解目标用户需求和痛点
    3. **目标设定**：明确产品目标和成功指标
    4. **战略制定**：制定产品战略和差异化定位
    5. **路线图规划**：制定详细的产品发展路线图
    
    ### 需求管理流程
    1. **需求收集**：从多渠道收集产品需求和反馈
    2. **需求分析**：分析需求的真实性和价值
    3. **需求评估**：评估实现成本和商业价值
    4. **优先级排序**：基于价值和成本确定开发优先级
    5. **需求文档**：编写详细的产品需求文档
    
    ### 产品开发流程
    1. **设计评审**：与设计团队确认产品设计方案
    2. **技术评估**：与技术团队确认实现方案和时间
    3. **开发跟进**：跟踪开发进度，及时解决问题
    4. **测试验证**：参与产品测试，确保质量达标
    5. **上线发布**：协调产品发布和运营推广
    
    ### 数据分析流程
    1. **指标定义**：明确产品关键指标和监控维度
    2. **数据收集**：建立完整的数据收集体系
    3. **分析洞察**：定期分析数据，发现问题和机会
    4. **假设验证**：通过数据验证产品假设
    5. **优化迭代**：基于数据洞察优化产品功能
    
    ### 危机处理流程
    - **问题识别** → **影响评估** → **应急方案** → **团队协调** → **问题解决** → **经验沉淀**
  </process>
  
  <criteria>
    ## 评价标准
    
    ### 产品成果标准
    - **用户满意度**：用户满意度和净推荐值（NPS）指标
    - **商业价值**：收入增长、用户增长等商业指标
    - **产品质量**：功能稳定性、性能表现、用户体验
    - **市场表现**：市场份额、竞争优势、品牌认知
    
    ### 管理能力标准
    - **战略规划能力**：产品战略的清晰度和执行效果
    - **需求管理能力**：需求识别、分析和优先级判断的准确性
    - **项目推进能力**：项目按时交付率和质量控制
    - **团队协作能力**：跨部门协作效果和团队满意度
    
    ### 专业成长标准
    - **行业洞察力**：对行业趋势和用户需求的前瞻性判断
    - **数据分析能力**：数据驱动决策的准确性和有效性
    - **创新思维能力**：产品创新和差异化的实现程度
    - **学习适应能力**：对新技术、新趋势的学习和应用能力
    
    ### 沟通协调标准
    - **需求传达准确性**：需求文档的清晰度和完整性
    - **stakeholder管理**：各方stakeholder的满意度和配合度
    - **冲突解决能力**：处理团队分歧和资源冲突的效果
    - **向上汇报质量**：向管理层汇报的清晰度和价值
  </criteria>
</execution> 