<execution>
  <constraint>
    ## 微信平台技术约束
    - **包大小限制**：主包不超过2MB，总包不超过20MB
    - **代码包限制**：分包不超过2MB，最多使用20个分包
    - **API调用限制**：网络请求并发限制，部分API有调用频次限制
    - **性能要求**：页面渲染时间不超过2秒，交互响应时间不超过300ms
    - **平台兼容性**：需兼容微信不同版本和iOS/Android双平台
    - **审核规范**：必须遵守微信小程序平台规则和内容规范
    - **开发工具依赖**：必须使用微信开发者工具进行开发和调试
  </constraint>

  <rule>
    ## 强制性开发规则
    - **代码规范强制**：必须遵循微信小程序开发规范和ESLint配置
    - **文件结构固定**：页面必须包含.js/.wxml/.wxss/.json四个文件
    - **生命周期规范**：严格按照小程序生命周期进行开发，避免内存泄漏
    - **API使用规范**：必须进行用户授权检查，合规使用敏感API
    - **安全要求**：敏感数据必须加密传输，用户隐私信息严格保护
    - **性能底线**：setData调用频次控制，避免频繁的数据更新
    - **审核合规**：代码和内容必须符合微信审核要求，避免违规操作
  </rule>

  <guideline>
    ## 开发最佳实践指导
    - **组件化优先**：优先封装可复用组件，提高开发效率和代码质量
    - **性能优化导向**：关注首屏加载时间、内存使用、用户体验流畅度
    - **用户体验优先**：遵循微信设计规范，保持界面一致性和易用性
    - **渐进增强**：优雅降级处理，确保在低版本微信中基本功能可用
    - **错误处理友好**：提供清晰的错误提示和用户引导
    - **测试覆盖全面**：真机测试、兼容性测试、性能测试并重
    - **版本管理规范**：使用语义化版本控制，合理规划版本发布
  </guideline>

  <process>
    ## 微信小程序开发执行流程

    ### Phase 1: 项目规划与准备
    ```
    需求分析：
    1. 明确功能需求和用户场景
    2. 分析技术可行性和平台限制
    3. 确定MVP功能范围和迭代计划
    4. 评估开发周期和资源需求
    
    技术选型：
    1. 框架选择：原生/uni-app/Taro/WePY
    2. UI组件库：WeUI/Vant Weapp/ColorUI
    3. 状态管理：原生/MobX/Redux
    4. 后端服务：传统后端/云开发
    
    项目初始化：
    1. 创建小程序项目，配置基础信息
    2. 搭建项目目录结构
    3. 配置开发环境和构建工具
    4. 设置代码规范和Git工作流
    ```

    ### Phase 2: 架构设计与环境配置
    ```
    目录结构设计：
    ├── pages/              # 页面文件
    │   ├── index/         # 首页
    │   └── detail/        # 详情页
    ├── components/         # 公共组件
    ├── utils/             # 工具函数
    ├── api/               # 接口封装
    ├── store/             # 状态管理
    ├── styles/            # 公共样式
    ├── static/            # 静态资源
    └── app.js/wxss/json   # 全局配置
    
    架构设计原则：
    1. 模块化：功能模块独立，便于维护
    2. 组件化：UI组件可复用，提高效率
    3. 服务化：API接口统一封装管理
    4. 配置化：可配置参数集中管理
    ```

    ### Phase 3: UI开发与组件封装
    ```
    页面开发流程：
    1. 分析设计稿，拆解页面结构
    2. 使用WXML构建页面骨架
    3. 使用WXSS实现样式效果
    4. 处理响应式布局和适配
    
    组件开发策略：
    1. 识别可复用的UI模块
    2. 封装自定义组件
    3. 定义组件属性和事件
    4. 编写组件文档和使用示例
    
    样式开发规范：
    1. 使用rpx单位适配不同屏幕
    2. 遵循BEM命名规范
    3. 使用CSS变量管理主题色彩
    4. 优化CSS性能，避免复杂选择器
    ```

    ### Phase 4: 功能开发与API集成
    ```
    业务逻辑开发：
    1. 实现页面交互逻辑
    2. 处理用户输入和表单验证
    3. 实现路由跳转和参数传递
    4. 处理页面状态管理
    
    API集成开发：
    1. 封装网络请求模块
    2. 实现接口调用和数据处理
    3. 添加请求拦截器和错误处理
    4. 实现数据缓存和同步策略
    
    数据管理：
    1. 设计数据流向和状态管理
    2. 实现本地存储和缓存策略
    3. 处理数据同步和更新
    4. 优化setData性能
    ```

    ### Phase 5: 性能优化与调试
    ```
    性能优化策略：
    1. 代码分包：合理拆分主包和分包
    2. 懒加载：按需加载页面和组件
    3. 图片优化：压缩图片，使用WebP格式
    4. 缓存策略：合理使用缓存减少请求
    
    调试与测试：
    1. 开发者工具调试
    2. 真机预览和调试
    3. 性能分析和优化
    4. 兼容性测试
    
    代码质量保证：
    1. ESLint代码检查
    2. 单元测试编写
    3. 代码审查
    4. 性能监控
    ```

    ### Phase 6: 测试发布与上线
    ```
    测试阶段：
    1. 功能测试：验证所有功能正常
    2. 兼容性测试：测试不同设备和版本
    3. 性能测试：检查加载速度和流畅度
    4. 安全测试：验证数据安全和权限控制
    
    发布准备：
    1. 准备小程序基础信息和资料
    2. 配置服务器域名和业务域名
    3. 设置用户隐私保护指引
    4. 准备审核说明和测试账号
    
    审核发布：
    1. 提交代码审核
    2. 响应审核反馈
    3. 发布正式版本
    4. 监控线上表现
    ```

    ### Phase 7: 运营维护与迭代
    ```
    上线监控：
    1. 监控小程序性能指标
    2. 收集用户反馈和错误日志
    3. 分析用户行为数据
    4. 优化用户体验
    
    迭代优化：
    1. 根据数据分析优化功能
    2. 修复发现的Bug
    3. 新功能开发和上线
    4. 持续性能优化
    
    版本管理：
    1. 制定版本发布计划
    2. 管理版本兼容性
    3. 处理版本回滚
    4. 维护发布文档
    ```
  </process>

  <criteria>
    ## 质量评价标准

    ### 代码质量指标
    - ✅ **规范性检查**：通过ESLint检查，无语法错误
    - ✅ **结构清晰**：目录结构合理，文件命名规范
    - ✅ **组件化程度**：公共组件复用率≥60%
    - ✅ **代码注释**：关键业务逻辑注释覆盖率≥80%
    - ✅ **函数复杂度**：单个函数行数不超过50行

    ### 性能质量指标
    - ✅ **首屏加载**：首屏渲染时间≤2秒
    - ✅ **包大小控制**：主包大小≤1.5MB，分包合理使用
    - ✅ **内存使用**：页面内存占用≤50MB
    - ✅ **网络请求**：接口响应时间≤1秒
    - ✅ **用户体验**：页面切换流畅，无明显卡顿

    ### 功能质量指标
    - ✅ **功能完整性**：核心功能100%实现
    - ✅ **交互友好性**：操作响应及时，反馈明确
    - ✅ **兼容性**：支持微信6.6.3以上版本
    - ✅ **错误处理**：异常情况有友好提示
    - ✅ **权限管理**：合规申请和使用用户权限

    ### 安全合规指标
    - ✅ **数据安全**：敏感数据加密传输和存储
    - ✅ **隐私保护**：用户隐私信息保护到位
    - ✅ **内容合规**：内容符合平台规范
    - ✅ **API合规**：API使用符合官方要求
    - ✅ **审核通过**：能够顺利通过微信审核

    ### 用户体验指标
    - ✅ **界面美观**：UI设计符合微信规范
    - ✅ **操作便捷**：用户操作路径简洁明了
    - ✅ **信息架构**：信息层次清晰，导航明确
    - ✅ **反馈及时**：操作反馈及时准确
    - ✅ **错误容错**：用户误操作有友好处理

    ## 持续改进标准

    ### 技术债务管理
    - 定期代码重构，消除技术债务
    - 升级依赖库版本，保持技术栈新鲜度
    - 优化陈旧代码，提高维护效率
    - 完善文档，降低维护成本

    ### 性能持续优化
    - 建立性能监控体系
    - 定期性能分析和优化
    - 关注新技术和最佳实践
    - 优化用户体验细节

    ### 团队协作效率
    - 完善开发规范和流程
    - 建立代码审查机制
    - 提升自动化程度
    - 知识分享和技能提升
  </criteria>
</execution> 