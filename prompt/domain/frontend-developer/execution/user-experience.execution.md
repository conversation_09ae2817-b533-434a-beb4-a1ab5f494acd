<execution>
  <constraint>
    # 用户体验客观约束
    
    ## 👥 用户群体约束
    - **能力差异**: 用户技术水平、学习能力、操作熟练度差异巨大
    - **设备多样性**: 不同屏幕尺寸、分辨率、输入方式的设备差异
    - **环境限制**: 网络环境、使用场景、干扰因素的不可控性
    - **无障碍需求**: 视觉、听觉、运动能力障碍用户的特殊需求
    
    ## 🌍 技术环境约束  
    - **浏览器差异**: 不同浏览器的渲染引擎和API支持差异
    - **性能限制**: 低端设备的计算能力和内存限制
    - **网络条件**: 带宽限制、延迟波动、不稳定连接
    - **系统集成**: 与操作系统、其他应用的集成限制
  </constraint>

  <rule>
    # 用户体验强制规则
    
    ## 🎯 可访问性强制要求
    - **WCAG 2.1 AA级别**: 必须符合Web内容可访问性指南AA级别
    - **键盘导航**: 所有功能必须支持键盘操作，无鼠标依赖
    - **屏幕阅读器**: 必须为视障用户提供完整的屏幕阅读器支持
    - **对比度要求**: 文本对比度必须达到4.5:1，大文本3:1
    
    ## ⚡ 性能用户感知规则
    - **首屏时间**: 首屏内容必须在2秒内显示
    - **交互响应**: 用户操作反馈必须在100ms内响应
    - **页面切换**: 页面或路由切换必须在500ms内完成
    - **滚动流畅**: 滚动必须保持60fps，无卡顿感知
    
    ## 📱 响应式设计规则
    - **移动优先**: 必须采用移动优先的设计和开发策略
    - **断点适配**: 必须在320px-2560px范围内完美适配
    - **触摸友好**: 触摸目标最小44px×44px，间距足够
    - **内容优先**: 核心内容在任何设备上都必须可访问
  </rule>

  <guideline>
    # 用户体验指导原则
    
    ## 🎨 设计系统指导
    - **一致性原则**: 建议建立统一的设计语言和交互模式
    - **简洁性原则**: 推荐简化界面，减少认知负担
    - **反馈性原则**: 建议为每个用户操作提供明确反馈
    - **容错性原则**: 推荐设计容错机制，减少用户错误成本
    
    ## 🔄 交互设计指导
    - **渐进披露**: 建议根据用户需求层次渐进展示信息
    - **操作效率**: 推荐为熟练用户提供快捷操作方式
    - **上下文感知**: 建议根据用户当前状态提供相关功能
    - **个性化**: 推荐支持用户自定义偏好设置
    
    ## 🚀 性能体验指导
    - **感知性能**: 建议优化用户感知的加载速度
    - **渐进加载**: 推荐采用骨架屏、懒加载等技术
    - **缓存策略**: 建议合理使用缓存提升重复访问体验
    - **离线支持**: 推荐为核心功能提供离线访问能力
  </guideline>

  <process>
    # 用户体验设计与实现流程
    
    ## 🔍 用户研究阶段
    
    ### 1. 用户调研与分析
    ```mermaid
    flowchart LR
        A[用户访谈] --> B[行为观察]
        B --> C[数据分析]
        C --> D[画像构建]
        D --> E[需求洞察]
    ```
    
    - **目标用户识别**: 明确核心用户群体和使用场景
    - **用户行为分析**: 观察真实使用行为和痛点
    - **需求优先级**: 区分核心需求和边缘需求
    - **竞品分析**: 分析同类产品的UX优劣势
    
    ## 🎨 设计实现阶段
    
    ### 2. 交互设计与原型
    - **信息架构**: 设计清晰的信息组织结构
    - **交互流程**: 设计用户操作的完整流程
    - **界面布局**: 设计响应式界面布局方案
    - **原型测试**: 验证设计方案的可用性
    
    ### 3. 视觉设计与规范
    - **设计系统**: 建立完整的设计系统和组件库
    - **视觉风格**: 确定符合品牌的视觉风格
    - **适配方案**: 设计多端适配的视觉方案
    - **无障碍设计**: 确保设计符合可访问性要求
    
    ## 💻 开发实现阶段
    
    ### 4. 前端实现与优化
    - **组件开发**: 基于设计系统开发可复用组件
    - **交互实现**: 实现流畅的交互动画和反馈
    - **性能优化**: 优化加载速度和运行性能
    - **兼容性处理**: 处理浏览器和设备兼容性问题
    
    ## 🧪 测试验证阶段
    
    ### 5. 用户体验测试
    - **可用性测试**: 验证设计的易用性和效率
    - **A/B测试**: 对比不同方案的用户反馈
    - **无障碍测试**: 验证可访问性实现效果
    - **性能测试**: 验证真实环境下的性能表现
  </process>

  <criteria>
    # 用户体验评估标准
    
    ## 👨‍💼 用户满意度标准
    - **易用性得分**: 用户任务完成率 ≥ 95%，错误率 ≤ 5%
    - **效率指标**: 核心任务完成时间符合预期目标
    - **满意度评分**: 用户满意度评分 ≥ 4.0/5.0
    - **推荐意愿**: 净推荐值(NPS) ≥ 50
    
    ## ⚡ 性能体验标准
    - **Core Web Vitals**: 
      - LCP (最大内容绘制) ≤ 2.5秒
      - FID (首次输入延迟) ≤ 100毫秒  
      - CLS (累积布局偏移) ≤ 0.1
    - **交互响应**: 界面操作响应时间 ≤ 100ms
    - **页面切换**: 路由切换时间 ≤ 500ms
    
    ## ♿ 可访问性标准
    - **WCAG合规**: 100%符合WCAG 2.1 AA级别要求
    - **键盘操作**: 所有功能都支持键盘导航
    - **屏幕阅读器**: 完整支持主流屏幕阅读器
    - **颜色对比**: 文本对比度符合最低要求
    
    ## 📱 响应式适配标准
    - **设备兼容**: 在目标设备上100%功能正常
    - **布局适配**: 在所有断点下布局美观实用
    - **触摸操作**: 移动端操作便捷，无误触
    - **性能一致**: 各设备上性能表现稳定
    
    ## 🎯 业务价值标准
    - **转化率**: 关键业务流程转化率达到预期
    - **留存率**: 用户留存率和活跃度指标良好
    - **支持成本**: 用户支持请求数量减少
    - **品牌价值**: 提升品牌形象和用户忠诚度
  </criteria>
</execution> 