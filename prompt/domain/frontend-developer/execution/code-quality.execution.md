<execution>
  <constraint>
    # 代码质量客观约束
    
    ## 🛠️ 工具生态约束
    - **工具链复杂性**: 质量工具配置和维护成本高
    - **团队技能差异**: 开发者代码质量意识和技能水平不同
    - **项目时间压力**: 紧急需求可能影响代码质量标准执行
    - **遗留代码负担**: 历史代码质量问题需要逐步改善
    
    ## 📊 度量局限性
    - **静态分析限制**: 工具无法检测所有代码问题
    - **覆盖率误导**: 高测试覆盖率不等于高质量
    - **指标片面性**: 单一指标无法完全反映代码质量
    - **上下文依赖**: 质量标准需要根据项目特点调整
  </constraint>

  <rule>
    # 代码质量强制规则
    
    ## 📝 代码规范强制要求
    - **ESLint零错误**: 所有代码必须通过ESLint检查，无error级别问题
    - **TypeScript严格模式**: 必须启用严格模式，类型检查无错误
    - **代码格式化**: 必须使用Prettier统一代码格式
    - **提交检查**: Git提交前必须通过lint-staged检查
    
    ## 🧪 测试质量规则
    - **单元测试覆盖率**: 核心业务逻辑覆盖率必须达到80%
    - **关键路径100%**: 关键业务流程必须有100%测试覆盖
    - **测试通过率**: 所有自动化测试必须通过，不允许broken tests
    - **性能测试**: 关键组件必须有性能基准测试
    
    ## 🔍 代码审查规则  
    - **强制Code Review**: 所有代码变更必须经过至少一人审查
    - **安全审查**: 涉及安全的代码必须经过安全专家审查
    - **架构审查**: 重要架构变更必须经过架构评审
    - **文档同步**: 代码变更必须同步更新相关文档
  </rule>

  <guideline>
    # 代码质量指导原则
    
    ## 🎯 设计原则指导
    - **SOLID原则**: 建议遵循单一职责、开闭原则等设计原则
    - **DRY原则**: 推荐避免代码重复，提取公共逻辑
    - **KISS原则**: 建议保持代码简单，避免过度设计
    - **YAGNI原则**: 推荐只实现当前需要的功能
    
    ## 📚 最佳实践指导
    - **函数式编程**: 建议使用纯函数和不可变数据
    - **组件设计**: 推荐小而专注的组件设计
    - **错误处理**: 建议完善的错误处理和边界情况
    - **性能考虑**: 推荐在编码时考虑性能影响
    
    ## 🔧 工具使用指导
    - **静态分析**: 建议配置完善的ESLint和TypeScript规则
    - **自动化测试**: 推荐完整的测试策略和工具链
    - **持续集成**: 建议集成质量检查到CI/CD流程
    - **代码度量**: 推荐定期分析代码质量指标
  </guideline>

  <process>
    # 代码质量保障流程
    
    ## 🚀 开发阶段质量控制
    
    ### 1. 编码标准执行
    ```mermaid
    flowchart TD
        A[开始编码] --> B[IDE实时检查]
        B --> C[本地lint检查] 
        C --> D[单元测试编写]
        D --> E[代码自测]
        E --> F[提交前检查]
        F --> G{质量检查通过?}
        G -->|否| B
        G -->|是| H[提交代码]
    ```
    
    ### 2. 代码审查流程
    - **创建Pull Request**: 提供详细的变更说明
    - **自动化检查**: CI流水线自动运行质量检查
    - **人工审查**: 资深开发者进行代码审查
    - **反馈处理**: 及时响应和处理审查意见
    - **合并决策**: 通过所有检查后合并代码
    
    ## 🔍 质量监控与改进
    
    ### 3. 持续质量监控
    - **代码指标跟踪**: 定期分析圈复杂度、重复率等指标
    - **技术债务管理**: 识别和规划技术债务偿还
    - **质量趋势分析**: 跟踪质量指标变化趋势
    - **团队培训**: 定期开展代码质量培训
    
    ### 4. 质量改进循环
    - **问题识别**: 发现代码质量问题和模式
    - **根因分析**: 分析问题产生的根本原因
    - **改进措施**: 制定针对性的改进方案
    - **效果验证**: 验证改进措施的效果
  </process>

  <criteria>
    # 代码质量评估标准
    
    ## 📊 量化质量指标
    - **代码覆盖率**: 单元测试覆盖率 ≥ 80%，关键路径100%
    - **圈复杂度**: 函数圈复杂度 ≤ 10，类复杂度 ≤ 50
    - **重复代码率**: 代码重复率 ≤ 5%
    - **技术债务**: SonarQube技术债务 ≤ 1天
    
    ## 🔍 代码审查质量
    - **审查覆盖率**: 100%代码变更经过审查
    - **审查效率**: 审查响应时间 ≤ 24小时
    - **缺陷发现率**: 审查阶段发现缺陷 ≥ 60%
    - **审查质量**: 线上bug回溯到审查阶段 ≤ 20%
    
    ## 🧪 测试质量标准
    - **测试通过率**: 自动化测试通过率 = 100%
    - **测试稳定性**: 测试失败率 ≤ 1%
    - **测试效率**: 测试执行时间在合理范围
    - **测试维护**: 测试代码质量达到产品代码标准
    
    ## 🚀 交付质量标准
    - **线上缺陷率**: 生产环境缺陷 ≤ 0.1%
    - **性能回归**: 性能指标无明显回退
    - **安全漏洞**: 无高危安全漏洞
    - **用户影响**: 质量问题对用户影响最小化
  </criteria>
</execution> 