<execution>
  <process>
    # 现代前端开发执行流程
    
    ## 🎯 项目启动与规划
    ```mermaid
    flowchart TD
        A[项目启动] --> B[需求分析]
        B --> C[技术调研]
        C --> D[架构设计]
        D --> E[环境搭建]
        E --> F[开发实施]
        F --> G[测试验证]
        G --> H[部署上线]
        H --> I[监控维护]
        I --> J{需求变更?}
        J -->|是| B
        J -->|否| K[项目结束]
    ```
    
    ### 1. 需求分析与设计
    - **用户调研**: 用户画像、使用场景、痛点分析
    - **竞品分析**: 功能对比、交互模式、技术方案
    - **原型设计**: 低保真原型、高保真设计、交互规范
    - **技术评估**: 性能要求、兼容性需求、资源约束
    
    ### 2. 技术架构设计
    - **系统架构**: 前端架构、数据流设计、模块划分
    - **技术选型**: 框架选择、工具链配置、依赖管理
    - **设计系统**: 组件库、样式规范、交互标准
    - **开发规范**: 代码规范、Git工作流、文档标准
    
    ## 🏗️ 开发实施阶段
    
    ### 3. 环境搭建与配置
    - **开发环境**: 本地开发环境、IDE配置、调试工具
    - **构建工具**: 打包配置、热更新、代码分割
    - **质量工具**: 代码检查、格式化、提交检查
    - **CI/CD**: 自动化构建、测试、部署流水线
    
    ### 4. 功能开发与实现
    - **组件开发**: 基础组件、业务组件、页面组件
    - **状态管理**: 全局状态、局部状态、数据流
    - **API集成**: 接口调用、错误处理、数据缓存
    - **路由管理**: 页面路由、权限控制、懒加载
    
    ## 🧪 测试与优化
    
    ### 5. 质量保障
    - **单元测试**: 组件测试、工具函数测试、覆盖率
    - **集成测试**: 页面流程、API集成、数据流测试
    - **端到端测试**: 用户场景、跨浏览器、性能测试
    - **可访问性测试**: 无障碍访问、键盘导航、屏幕阅读器
    
    ### 6. 性能优化
    - **加载优化**: 代码分割、资源预加载、缓存策略
    - **运行优化**: 虚拟化、防抖节流、内存管理
    - **网络优化**: HTTP/2、CDN、资源压缩
    - **体验优化**: 骨架屏、加载动画、错误边界
    
    ## 🚀 部署与维护
    
    ### 7. 生产部署
    - **构建优化**: 生产构建、资源优化、环境配置
    - **部署策略**: 蓝绿部署、灰度发布、回滚机制
    - **监控系统**: 性能监控、错误追踪、用户行为
    - **安全防护**: HTTPS、CSP、依赖安全检查
    
    ### 8. 持续维护
    - **性能监控**: Core Web Vitals、用户体验指标
    - **错误处理**: 错误收集、分析处理、修复发布
    - **功能迭代**: 需求分析、功能开发、A/B测试
    - **技术升级**: 依赖更新、框架升级、重构优化
  </process>

  <guideline>
    # 现代前端开发指导原则
    
    ## 🎨 用户体验优先
    - **性能至上**: 首屏加载时间 < 2秒，交互响应 < 100ms
    - **渐进增强**: 核心功能优先，增强功能渐进加载
    - **响应式设计**: 移动优先，多端适配，无缝体验
    - **可访问性**: 遵循WCAG 2.1标准，包容性设计
    
    ## 🏗️ 代码质量原则
    - **组件化思维**: 单一职责、高内聚低耦合、可复用性
    - **函数式编程**: 纯函数、不可变数据、函数组合
    - **类型安全**: TypeScript优先，严格类型检查
    - **测试驱动**: 先写测试，后写实现，测试覆盖率 > 80%
    
    ## ⚡ 现代化工程
    - **工具链自动化**: 构建、测试、部署全流程自动化
    - **模块化架构**: ES模块、动态导入、Tree Shaking
    - **版本管理**: 语义化版本、变更日志、发布管理
    - **文档先行**: API文档、组件文档、最佳实践
    
    ## 🔒 安全与性能
    - **安全编码**: 输入验证、XSS防护、CSRF保护
    - **性能预算**: Bundle大小限制、性能指标监控
    - **缓存策略**: 浏览器缓存、CDN缓存、应用缓存
    - **监控体系**: 实时监控、异常告警、用户反馈
  </guideline>

  <rule>
    # 前端开发强制规则
    
    ## 📋 代码规范强制要求
    - **必须使用**: TypeScript + ESLint + Prettier 组合
    - **必须遵循**: 统一的命名规范和文件组织结构
    - **必须通过**: 所有代码审查和自动化检查
    - **必须达到**: 单元测试覆盖率 ≥ 80%，关键路径 100%
    
    ## 🔍 质量门禁要求
    - **必须满足**: Core Web Vitals 所有指标达到Good
    - **必须支持**: 主流浏览器最新3个版本
    - **必须通过**: 无障碍性检查（WCAG 2.1 AA级别）
    - **必须验证**: 移动端和桌面端完整功能
    
    ## 🛡️ 安全规则
    - **严禁暴露**: API密钥、敏感配置、用户隐私数据
    - **必须防护**: XSS、CSRF、点击劫持等Web安全漏洞
    - **必须启用**: Content Security Policy (CSP)
    - **必须验证**: 所有用户输入和第三方数据
    
    ## 📱 兼容性规则
    - **必须适配**: iOS Safari、Android Chrome、PC Chrome/Firefox
    - **必须处理**: 网络异常、加载失败、API错误
    - **必须提供**: 优雅降级和错误边界
    - **必须支持**: 离线访问核心功能（PWA）
  </rule>

  <constraint>
    # 前端开发约束条件
    
    ## 🌐 技术环境约束
    - **浏览器兼容**: 现代浏览器 ES2020+ 特性支持
    - **设备适配**: 320px - 2560px 宽度范围全覆盖
    - **网络环境**: 3G网络下可用，2G网络核心功能可用
    - **性能预算**: 
      - 首屏JS bundle < 200KB gzipped
      - 总资源大小 < 1MB
      - 首屏图片 < 500KB
    
    ## 🔧 开发工具约束
    - **Node.js版本**: >= 18.0.0 LTS版本
    - **包管理器**: 团队统一使用 pnpm 或 yarn
    - **构建工具**: Vite（开发）+ Rollup（生产）
    - **代码编辑器**: VS Code + 必要扩展插件
    
    ## 📊 性能约束指标
    - **Core Web Vitals**:
      - LCP (Largest Contentful Paint) ≤ 2.5s
      - FID (First Input Delay) ≤ 100ms
      - CLS (Cumulative Layout Shift) ≤ 0.1
    - **其他指标**:
      - FCP (First Contentful Paint) ≤ 1.8s
      - TTI (Time to Interactive) ≤ 3.8s
    
    ## 🔐 安全约束
    - **数据传输**: 生产环境强制HTTPS
    - **存储安全**: 敏感数据禁止localStorage存储
    - **依赖管理**: 禁用有安全漏洞的依赖包
    - **CSP策略**: 严格的内容安全策略配置
  </constraint>

  <criteria>
    # 前端开发评估标准
    
    ## ✅ 功能完整性标准
    - **需求覆盖率**: 100% 实现PRD中所有功能点
    - **交互一致性**: UI交互与设计稿一致性 ≥ 98%
    - **数据完整性**: 正确处理所有数据状态（加载/成功/失败/空）
    - **路由功能**: 页面导航、浏览器历史、深度链接完全正常
    
    ## 🎨 用户体验标准
    - **视觉还原度**: 与设计稿像素级一致，误差 ≤ 2px
    - **响应式适配**: 所有目标设备完美显示，无布局破损
    - **动画流畅度**: 60fps流畅动画，无卡顿感知
    - **操作反馈**: 所有用户操作都有即时、清晰的视觉反馈
    
    ## ⚡ 性能质量标准
    - **Lighthouse评分**: Performance ≥ 95, Accessibility ≥ 95
    - **真实用户监控**: Core Web Vitals在75分位数达到Good
    - **资源加载**: 关键资源预加载，非关键资源懒加载
    - **缓存效率**: 静态资源缓存命中率 ≥ 90%
    
    ## 🧪 代码质量标准
    - **测试覆盖**: 
      - 单元测试覆盖率 ≥ 80%
      - 关键业务逻辑覆盖率 = 100%
      - E2E测试覆盖主要用户流程
    - **代码质量**:
      - ESLint检查 0 error
      - TypeScript严格模式 0 error
      - 圈复杂度 ≤ 10
    - **文档完整**: 所有公共API和组件都有完整文档
    
    ## 🔒 安全质量标准
    - **安全扫描**: 通过OWASP安全检查，无高危漏洞
    - **依赖安全**: 所有依赖包无已知安全漏洞
    - **隐私保护**: 符合GDPR等隐私保护法规
    - **数据安全**: 敏感数据传输加密，存储脱敏
    
    ## 🌍 兼容性与可维护性标准
    - **浏览器兼容**: 目标浏览器100%功能正常
    - **设备兼容**: 各种屏幕尺寸和分辨率完美适配
    - **网络适应**: 各种网络条件下基本可用
    - **代码可维护**: 新功能开发效率，bug修复速度符合预期
  </criteria>
</execution> 