<execution>
  <constraint>
    # 技术架构客观约束
    
    ## 🌐 浏览器环境约束
    - **执行环境**: 单线程JavaScript引擎，异步非阻塞模式
    - **内存限制**: 浏览器内存上限，避免内存泄漏和过度消耗
    - **安全沙箱**: 同源策略、CSP限制、CORS跨域约束
    - **API兼容性**: 浏览器API支持差异，需要优雅降级
    
    ## 📱 设备性能约束
    - **计算能力**: 移动设备CPU/GPU性能限制
    - **网络环境**: 移动网络带宽不稳定，延迟较高
    - **存储空间**: 本地存储容量限制（localStorage, indexedDB）
    - **电池续航**: 高计算消耗影响设备续航
    
    ## 🔧 技术生态约束
    - **框架生命周期**: 技术栈更新频率和兼容性变化
    - **构建工具限制**: 打包工具的配置复杂度和性能瓶颈
    - **依赖管理**: 第三方库的安全性、维护状态、版本冲突
    - **部署环境**: CDN、服务器、容器化平台的技术限制
  </constraint>

  <rule>
    # 技术架构强制规则
    
    ## 🏗️ 架构设计规则
    - **模块化强制要求**: 必须采用模块化设计，避免代码耦合
    - **组件化原则**: 必须基于组件化思维构建UI，单一职责
    - **状态管理规范**: 必须明确状态管理边界，避免状态混乱
    - **API接口规范**: 必须统一API调用方式和错误处理机制
    
    ## 🔒 安全架构规则
    - **输入验证**: 必须对所有用户输入进行严格验证和转义
    - **权限控制**: 必须实现前端路由和组件级别的权限控制
    - **敏感数据**: 禁止在前端存储敏感信息（密码、token等）
    - **依赖安全**: 必须定期检查和更新有安全漏洞的依赖包
    
    ## ⚡ 性能架构规则
    - **代码分割**: 必须实现路由和组件级别的代码分割
    - **资源优化**: 必须压缩和优化静态资源（JS、CSS、图片）
    - **缓存策略**: 必须实现合理的浏览器和CDN缓存策略
    - **打包大小**: 单个chunk不得超过200KB，总体积控制在合理范围
  </rule>

  <guideline>
    # 技术架构指导原则
    
    ## 🎯 设计原则指导
    - **渐进增强**: 建议基础功能优先，高级功能渐进增强
    - **响应式优先**: 推荐移动优先的响应式设计策略
    - **可访问性**: 建议从设计阶段就考虑无障碍访问需求
    - **国际化准备**: 推荐预留国际化扩展的架构空间
    
    ## 🔧 技术选型指导
    - **主流框架**: 建议选择成熟稳定的主流框架（React/Vue/Angular）
    - **构建工具**: 推荐现代化构建工具（Vite优于Webpack）
    - **状态管理**: 建议根据项目复杂度选择合适的状态管理方案
    - **UI组件库**: 推荐使用成熟的组件库作为基础，定制化开发
    
    ## 📈 可扩展性指导
    - **微前端**: 大型项目建议考虑微前端架构
    - **插件系统**: 推荐设计插件化架构支持功能扩展
    - **API抽象**: 建议抽象API层便于后端服务替换
    - **配置化**: 推荐重要功能支持配置化，减少代码修改
  </guideline>

  <process>
    # 技术架构设计流程
    
    ## 🎯 架构规划阶段
    ```mermaid
    flowchart TD
        A[需求分析] --> B[技术调研]
        B --> C[架构设计]
        C --> D[技术选型]
        D --> E[原型验证]
        E --> F[架构评审]
        F --> G{是否通过?}
        G -->|是| H[实施开发]
        G -->|否| C
    ```
    
    ### 1. 需求分析与技术调研
    - **功能需求梳理**: 明确核心功能、扩展功能、性能要求
    - **非功能需求**: 性能指标、安全要求、兼容性标准
    - **技术环境**: 目标浏览器、设备类型、部署环境
    - **团队技能**: 评估团队技术能力和学习成本
    
    ### 2. 架构设计与技术选型
    - **整体架构**: 确定应用架构模式（SPA/MPA/SSR等）
    - **技术栈选择**: 框架、构建工具、状态管理、UI库
    - **目录结构**: 设计清晰的文件组织结构
    - **开发规范**: 制定代码规范、Git工作流、部署流程
    
    ## 🏗️ 架构实施阶段
    
    ### 3. 基础环境搭建
    - **项目初始化**: 创建项目骨架，配置开发环境
    - **构建配置**: 配置打包工具、代码检查、自动化流程
    - **基础组件**: 开发通用组件、工具函数、样式系统
    - **API层封装**: 封装HTTP客户端、错误处理、数据转换
    
    ### 4. 核心功能开发
    - **路由系统**: 实现页面路由、权限控制、懒加载
    - **状态管理**: 建立全局状态、数据流管理
    - **业务组件**: 开发核心业务功能组件
    - **集成测试**: 确保各模块协同工作正常
    
    ## 🔍 架构优化阶段
    
    ### 5. 性能优化与监控
    - **性能分析**: 使用工具分析性能瓶颈
    - **代码优化**: 优化关键路径代码和资源加载
    - **监控体系**: 建立性能监控和错误追踪
    - **持续改进**: 基于监控数据持续优化架构
  </process>

  <criteria>
    # 技术架构评估标准
    
    ## ✅ 架构质量标准
    - **可维护性**: 代码结构清晰，易于理解和修改
    - **可扩展性**: 支持功能扩展，架构弹性良好
    - **可测试性**: 组件解耦，便于单元测试和集成测试
    - **可复用性**: 组件和工具函数具有良好的复用性
    
    ## ⚡ 性能质量标准
    - **加载性能**: 首屏加载时间 ≤ 2秒，页面切换 ≤ 500ms
    - **运行性能**: 60fps流畅交互，内存使用稳定
    - **网络优化**: 资源压缩率 ≥ 70%，HTTP请求数量合理
    - **缓存效率**: 静态资源缓存命中率 ≥ 90%
    
    ## 🔒 安全质量标准
    - **输入安全**: 所有用户输入都经过验证和转义
    - **权限安全**: 前端路由和API调用都有权限控制
    - **数据安全**: 敏感数据不在前端存储，传输加密
    - **依赖安全**: 无已知安全漏洞的第三方依赖
    
    ## 🌍 兼容性标准
    - **浏览器兼容**: 目标浏览器100%功能正常
    - **设备适配**: 各种屏幕尺寸和分辨率完美显示
    - **网络适应**: 弱网环境下基本功能可用
    - **降级支持**: 不支持的功能有优雅降级方案
  </criteria>
</execution> 