<execution>
  <process>
    # 代码质量管理流程
    
    ## 1. 代码规范制定
    ```mermaid
    flowchart TD
        A[团队编码规范制定] --> B[代码格式化配置]
        B --> C[静态代码分析工具配置]
        C --> D[代码审查流程建立]
        D --> E[质量门禁设置]
        E --> F[持续集成集成]
    ```
    
    ## 2. 代码审查流程
    ```mermaid
    flowchart TD
        A[代码提交] --> B[自动化检查]
        B --> C{检查通过?}
        C -->|是| D[人工代码审查]
        C -->|否| E[修复问题]
        E --> A
        D --> F{审查通过?}
        F -->|是| G[合并代码]
        F -->|否| H[修改建议]
        H --> E
    ```
  </process>

  <guideline>
    # 代码质量最佳实践
    
    ## 编码规范
    - **命名约定**：使用有意义的变量名和方法名
    - **代码格式**：统一使用代码格式化工具
    - **注释规范**：关键逻辑必须有清晰的注释
    - **方法长度**：单个方法不超过50行代码
    
    ## 代码结构
    - **单一职责**：每个类和方法只负责一个职责
    - **依赖管理**：合理管理类之间的依赖关系
    - **设计模式**：恰当使用设计模式解决问题
    - **重构意识**：定期重构消除代码坏味道
  </guideline>

  <rule>
    # 代码质量强制要求
    
    1. **代码覆盖率**：单元测试覆盖率不低于80%
    2. **复杂度控制**：圈复杂度不超过10
    3. **重复代码**：重复代码率不超过3%
    4. **技术债务**：每个迭代必须分配时间处理技术债务
  </rule>

  <criteria>
    # 代码质量评价标准
    
    - ✅ **可读性良好**：代码清晰易懂
    - ✅ **可维护性强**：易于修改和扩展
    - ✅ **性能表现佳**：无明显性能问题
    - ✅ **安全性高**：无安全漏洞
  </criteria>
</execution> 