<execution>
  <process>
    # Java后端开发核心流程
    
    ## 1. 需求分析与设计阶段
    ```mermaid
    flowchart TD
        A[接收需求] --> B[业务分析]
        B --> C[技术可行性评估]
        C --> D[架构设计]
        D --> E[API设计]
        E --> F[数据库设计]
        F --> G[技术选型]
        G --> H[开发计划制定]
    ```
    
    ## 2. 开发实施阶段
    ```mermaid
    flowchart TD
        A[环境搭建] --> B[项目框架搭建]
        B --> C[核心业务逻辑开发]
        C --> D[API接口实现]
        D --> E[数据访问层开发]
        E --> F[业务服务层开发]
        F --> G[控制器层开发]
        G --> H[单元测试编写]
        H --> I[集成测试]
        I --> J[代码审查]
    ```
    
    ## 3. 部署与运维阶段
    ```mermaid
    flowchart TD
        A[构建打包] --> B[环境部署]
        B --> C[性能测试]
        C --> D[监控配置]
        D --> E[日志配置]
        E --> F[上线发布]
        F --> G[运行监控]
        G --> H[问题处理]
        H --> I[优化迭代]
    ```
  </process>

  <guideline>
    # 开发指导原则
    
    ## 代码质量原则
    - **可读性优先**：代码应该像技术文档一样清晰易懂
    - **SOLID原则**：遵循面向对象设计的五大原则
    - **DRY原则**：避免重复代码，提取公共逻辑
    - **KISS原则**：保持简单，避免过度复杂的设计
    
    ## 架构设计原则
    - **分层架构**：明确控制层、业务层、数据访问层的职责
    - **依赖注入**：使用IoC容器管理对象依赖关系
    - **接口隔离**：定义清晰的接口契约，降低耦合度
    - **配置外部化**：将配置信息从代码中分离出来
    
    ## 性能优化建议
    - **数据库优化**：合理设计索引，优化SQL查询
    - **缓存策略**：在适当层次引入缓存机制
    - **异步处理**：对于耗时操作使用异步处理方式
    - **连接池管理**：合理配置数据库和Redis连接池
    
    ## 安全开发原则
    - **输入验证**：对所有外部输入进行严格验证
    - **权限控制**：实施细粒度的权限管理
    - **数据加密**：敏感数据传输和存储加密
    - **日志审计**：记录关键操作的审计日志
  </guideline>

  <rule>
    # 强制执行规则
    
    ## 代码规范要求
    1. **命名规范**：严格遵循Java命名约定
    2. **注释要求**：公开API必须有完整的JavaDoc注释
    3. **异常处理**：不允许空catch块，必须合理处理异常
    4. **日志记录**：关键操作必须记录日志，包含必要的上下文信息
    
    ## 安全要求
    1. **输入验证**：所有外部输入必须进行验证和过滤
    2. **SQL注入防护**：必须使用参数化查询或ORM框架
    3. **权限控制**：API接口必须有适当的权限检查
    4. **敏感信息**：不允许在代码中硬编码密码等敏感信息
    
    ## 测试要求
    1. **单元测试覆盖率**：核心业务逻辑测试覆盖率不低于80%
    2. **集成测试**：关键流程必须有集成测试
    3. **API测试**：所有对外接口必须有完整的API测试
    4. **性能测试**：核心接口必须通过性能测试验证
    
    ## 部署要求
    1. **环境一致性**：开发、测试、生产环境保持一致
    2. **配置管理**：使用配置中心管理不同环境的配置
    3. **版本控制**：所有代码变更必须通过版本控制系统
    4. **回滚机制**：部署必须支持快速回滚到上一版本
  </rule>

  <constraint>
    # 技术约束条件
    
    ## 环境约束
    - **JDK版本**：根据项目要求选择合适的JDK版本
    - **框架版本**：保持框架版本的一致性和稳定性
    - **数据库兼容性**：确保SQL语句跨数据库兼容
    - **服务器资源**：考虑部署环境的内存和CPU限制
    
    ## 业务约束
    - **并发处理能力**：系统必须支持预期的并发用户数
    - **响应时间要求**：API响应时间必须满足业务要求
    - **数据一致性**：涉及事务的操作必须保证数据一致性
    - **扩展性要求**：系统设计必须考虑未来的扩展需求
    
    ## 合规约束
    - **数据保护**：遵循数据保护相关法规要求
    - **审计日志**：关键操作必须留下完整的审计轨迹
    - **备份恢复**：重要数据必须有可靠的备份机制
    - **监控告警**：生产环境必须有完善的监控告警机制
    
    ## 技术债务约束
    - **代码质量**：定期进行代码质量评估和重构
    - **依赖管理**：及时更新和管理第三方依赖
    - **文档维护**：保持技术文档与代码同步更新
    - **知识传承**：确保关键技术知识在团队中传承
  </constraint>

  <criteria>
    # 质量评价标准
    
    ## 功能完整性评价
    - ✅ **需求实现度**：是否完整实现了所有功能需求
    - ✅ **API完整性**：接口是否提供了完整的CRUD操作
    - ✅ **异常处理**：是否妥善处理了各种异常情况
    - ✅ **边界条件**：是否正确处理了边界条件和极值情况
    
    ## 代码质量评价
    - ✅ **可读性**：代码结构清晰，命名规范，注释完整
    - ✅ **可维护性**：模块化程度高，耦合度低，易于扩展
    - ✅ **性能表现**：响应时间满足要求，资源使用合理
    - ✅ **安全性**：无安全漏洞，输入验证完整
    
    ## 架构设计评价
    - ✅ **分层清晰**：各层职责明确，依赖关系合理
    - ✅ **扩展性**：易于添加新功能，支持业务增长
    - ✅ **可测试性**：便于编写单元测试和集成测试
    - ✅ **监控能力**：具备完善的日志记录和监控指标
    
    ## 运维友好性评价
    - ✅ **部署简便**：部署流程自动化，配置管理规范
    - ✅ **故障诊断**：日志完整，便于问题定位和排查
    - ✅ **性能监控**：关键指标可监控，告警机制完善
    - ✅ **文档完整**：部署文档、运维手册齐全
  </criteria>
</execution> 