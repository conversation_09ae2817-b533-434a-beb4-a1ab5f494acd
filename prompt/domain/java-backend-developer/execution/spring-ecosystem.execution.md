<execution>
  <process>
    # Spring生态系统开发流程
    
    ## 1. Spring Boot项目初始化
    ```mermaid
    flowchart TD
        A[项目需求分析] --> B[依赖组件选择]
        B --> C[Spring Initializr配置]
        C --> D[项目结构规划]
        D --> E[配置文件设置]
        E --> F[基础组件配置]
        F --> G[开发环境验证]
    ```
    
    ## 2. Spring应用开发流程
    ```mermaid
    flowchart TD
        A[实体类设计] --> B[Repository层开发]
        B --> C[Service层开发]
        C --> D[Controller层开发]
        D --> E[配置类编写]
        E --> F[AOP切面开发]
        F --> G[异常处理配置]
        G --> H[测试用例编写]
    ```
    
    ## 3. Spring Security集成流程
    ```mermaid
    flowchart TD
        A[安全需求分析] --> B[认证方式选择]
        B --> C[Security配置]
        C --> D[用户详情服务]
        D --> E[权限控制实现]
        E --> F[JWT集成]
        F --> G[安全测试验证]
    ```
  </process>

  <guideline>
    # Spring开发指导原则
    
    ## 依赖注入最佳实践
    - **构造器注入优先**：使用构造器注入而非字段注入
    - **接口编程**：依赖接口而非具体实现类
    - **单例模式**：合理使用Spring的单例Bean
    - **懒加载**：对于重量级Bean考虑使用懒加载
    
    ## 配置管理建议
    - **外部化配置**：使用application.properties/yml管理配置
    - **Profile分离**：不同环境使用不同的配置文件
    - **配置验证**：使用@ConfigurationProperties进行配置绑定
    - **敏感信息保护**：使用Spring Cloud Config或Vault管理敏感配置
    
    ## 事务管理原则
    - **声明式事务**：优先使用@Transactional注解
    - **事务边界明确**：在Service层定义事务边界
    - **异常回滚**：明确指定哪些异常触发回滚
    - **事务传播**：合理设置事务传播行为
    
    ## 缓存策略建议
    - **缓存注解**：使用Spring Cache抽象层
    - **缓存键设计**：设计合理的缓存键命名规则
    - **缓存失效**：合理设置缓存过期时间
    - **缓存更新**：使用@CacheEvict及时清理过期缓存
  </guideline>

  <rule>
    # Spring开发强制规则
    
    ## 注解使用规范
    1. **组件注解**：严格区分@Controller、@Service、@Repository的使用场景
    2. **配置注解**：@Configuration类必须有明确的职责划分
    3. **验证注解**：所有DTO必须使用Bean Validation注解
    4. **文档注解**：所有REST接口必须使用Swagger/OpenAPI注解
    
    ## 异常处理规范
    1. **全局异常处理**：必须使用@ControllerAdvice统一处理异常
    2. **业务异常**：自定义业务异常必须继承BusinessException
    3. **异常信息**：异常信息必须包含错误码和用户友好的描述
    4. **异常日志**：系统异常必须记录完整的堆栈信息
    
    ## 安全规范要求
    1. **输入验证**：所有Controller入参必须进行验证
    2. **权限控制**：敏感接口必须使用@PreAuthorize进行权限检查
    3. **CSRF防护**：POST/PUT/DELETE接口必须启用CSRF防护
    4. **SQL注入防护**：禁止直接拼接SQL，必须使用参数化查询
    
    ## 性能要求
    1. **连接池配置**：数据库连接池必须根据实际情况调优
    2. **查询优化**：复杂查询必须进行性能测试和优化
    3. **缓存使用**：频繁查询的数据必须使用缓存
    4. **异步处理**：耗时操作必须使用@Async异步处理
  </rule>

  <constraint>
    # Spring技术约束
    
    ## 版本兼容性约束
    - **Spring Boot版本**：使用稳定的LTS版本，避免使用快照版本
    - **JDK版本匹配**：确保Spring Boot版本与JDK版本兼容
    - **依赖版本管理**：使用Spring Boot的依赖管理机制
    - **升级路径**：制定明确的版本升级路径和测试计划
    
    ## 资源使用约束
    - **内存使用**：Bean实例化必须考虑内存占用
    - **启动时间**：应用启动时间不应超过合理范围
    - **线程池配置**：合理配置线程池大小，避免资源浪费
    - **数据库连接**：严格控制数据库连接数量
    
    ## 架构约束
    - **分层架构**：严格遵循Controller-Service-Repository分层
    - **循环依赖**：避免Bean之间的循环依赖
    - **单一职责**：每个Bean只负责一个明确的职责
    - **依赖方向**：上层可以依赖下层，下层不能依赖上层
  </constraint>

  <criteria>
    # Spring应用质量标准
    
    ## 代码质量标准
    - ✅ **注解使用正确**：Spring注解使用符合最佳实践
    - ✅ **配置合理**：应用配置结构清晰，便于维护
    - ✅ **依赖注入规范**：依赖注入方式符合Spring推荐规范
    - ✅ **异常处理完善**：异常处理机制完整且用户友好
    
    ## 功能实现标准
    - ✅ **业务逻辑清晰**：Service层业务逻辑明确且可测试
    - ✅ **数据访问规范**：Repository层实现符合JPA/MyBatis规范
    - ✅ **接口设计合理**：REST API设计符合RESTful规范
    - ✅ **事务处理正确**：事务边界和传播行为设置合理
    
    ## 性能与安全标准
    - ✅ **性能表现良好**：接口响应时间满足业务要求
    - ✅ **缓存策略有效**：缓存使用合理且提升了系统性能
    - ✅ **安全防护到位**：输入验证、权限控制等安全机制完善
    - ✅ **监控指标完整**：具备完整的应用监控和健康检查
    
    ## 可维护性标准
    - ✅ **代码结构清晰**：项目结构符合Spring Boot规范
    - ✅ **配置管理规范**：环境配置分离且易于管理
    - ✅ **文档完整**：API文档和配置说明完整
    - ✅ **测试覆盖充分**：单元测试和集成测试覆盖核心功能
  </criteria>
</execution> 