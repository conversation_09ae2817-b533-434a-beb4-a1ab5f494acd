<execution>
  <process>
    # 数据库设计流程
    
    ## 1. 需求分析与概念设计
    ```mermaid
    flowchart TD
        A[业务需求分析] --> B[数据需求收集]
        B --> C[实体识别]
        C --> D[关系建模]
        D --> E[概念模型设计]
        E --> F[业务规则定义]
    ```
    
    ## 2. 逻辑设计与物理设计
    ```mermaid
    flowchart TD
        A[逻辑模型设计] --> B[规范化处理]
        B --> C[表结构设计]
        C --> D[索引设计]
        D --> E[约束定义]
        E --> F[分区策略]
        F --> G[性能优化]
    ```
    
    ## 3. 数据库实施与维护
    ```mermaid
    flowchart TD
        A[数据库创建] --> B[初始数据导入]
        B --> C[权限配置]
        C --> D[备份策略]
        D --> E[监控配置]
        E --> F[维护计划]
    ```
  </process>

  <guideline>
    # 数据库设计最佳实践
    
    ## 设计原则
    - **范式化设计**：遵循数据库范式，减少数据冗余
    - **性能考虑**：在规范化和性能之间找到平衡
    - **可扩展性**：设计时考虑未来的扩展需求
    - **一致性保证**：确保数据的完整性和一致性
    
    ## 命名规范
    - **表名**：使用复数形式，如users、orders
    - **字段名**：使用下划线分隔，如user_id、created_at
    - **索引名**：使用idx_前缀，如idx_user_email
    - **约束名**：使用有意义的名称，如fk_order_user_id
    
    ## 索引策略
    - **主键索引**：每个表必须有主键
    - **外键索引**：为所有外键创建索引
    - **查询优化**：为常用查询条件创建复合索引
    - **唯一约束**：为唯一性要求创建唯一索引
    
    ## 数据类型选择
    - **整数类型**：根据数值范围选择合适的整数类型
    - **字符串类型**：VARCHAR vs CHAR的选择原则
    - **日期时间**：统一使用DATETIME或TIMESTAMP
    - **JSON类型**：合理使用JSON字段存储非结构化数据
  </guideline>

  <rule>
    # 数据库设计强制规则
    
    ## 表设计要求
    1. **主键必须**：每个表都必须有主键
    2. **字段非空**：合理设置字段的非空约束
    3. **数据类型**：选择合适的数据类型和长度
    4. **默认值**：为可选字段设置合理的默认值
    
    ## 性能要求
    1. **索引覆盖**：核心查询必须有对应的索引
    2. **查询时间**：单表查询时间不超过100ms
    3. **并发支持**：支持预期的并发读写操作
    4. **存储优化**：合理使用存储引擎特性
    
    ## 安全要求
    1. **权限控制**：实施最小权限原则
    2. **敏感数据**：敏感字段必须加密存储
    3. **审计日志**：重要操作必须记录审计日志
    4. **备份恢复**：制定完整的备份恢复策略
    
    ## 规范要求
    1. **命名一致性**：严格遵循命名规范
    2. **文档完整**：数据库设计文档必须完整
    3. **版本控制**：数据库变更必须有版本控制
    4. **变更审批**：结构变更必须经过审批流程
  </rule>

  <constraint>
    # 数据库约束条件
    
    ## 技术约束
    - **数据库版本**：使用稳定的数据库版本
    - **存储限制**：考虑存储容量和成本限制
    - **备份窗口**：在业务允许的时间窗口内完成备份
    - **维护时间**：数据库维护不能影响业务运行
    
    ## 业务约束
    - **数据保留**：遵循数据保留政策
    - **合规要求**：满足数据保护法规要求
    - **性能指标**：达到业务要求的性能指标
    - **可用性要求**：满足业务连续性要求
    
    ## 运维约束
    - **监控能力**：数据库运行状态必须可监控
    - **告警机制**：关键指标超阈值必须告警
    - **自动化程度**：日常维护任务尽量自动化
    - **故障恢复**：具备快速故障恢复能力
    
    ## 扩展约束
    - **水平扩展**：设计支持分库分表的扩展方案
    - **读写分离**：支持读写分离架构
    - **缓存集成**：与缓存系统良好集成
    - **数据迁移**：支持平滑的数据迁移
  </constraint>

  <criteria>
    # 数据库设计质量标准
    
    ## 设计质量
    - ✅ **结构合理**：表结构设计符合业务逻辑
    - ✅ **关系正确**：实体关系建模准确
    - ✅ **约束完整**：数据完整性约束齐全
    - ✅ **规范一致**：命名和设计风格一致
    
    ## 性能表现
    - ✅ **查询效率**：常用查询响应时间快
    - ✅ **存储优化**：存储空间使用合理
    - ✅ **索引有效**：索引策略提升查询性能
    - ✅ **并发处理**：支持高并发访问
    
    ## 可维护性
    - ✅ **文档完整**：数据库设计文档完整
    - ✅ **版本管理**：数据库变更有版本控制
    - ✅ **监控完善**：数据库运行状态可监控
    - ✅ **备份可靠**：备份恢复机制可靠
    
    ## 扩展性
    - ✅ **水平扩展**：支持分库分表扩展
    - ✅ **读写分离**：支持读写分离架构
    - ✅ **缓存友好**：与缓存系统集成良好
    - ✅ **迁移便利**：支持数据平滑迁移
    
    ## 安全性
    - ✅ **权限控制**：数据库访问权限控制完善
    - ✅ **数据加密**：敏感数据加密存储
    - ✅ **审计完整**：数据库操作审计完整
    - ✅ **备份安全**：备份数据安全可靠
  </criteria>
</execution> 