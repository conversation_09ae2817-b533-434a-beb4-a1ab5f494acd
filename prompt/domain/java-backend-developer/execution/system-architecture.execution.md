<execution>
  <process>
    # 系统架构设计流程
    
    ## 1. 架构需求分析
    ```mermaid
    flowchart TD
        A[业务需求收集] --> B[非功能需求识别]
        B --> C[约束条件分析]
        C --> D[质量属性定义]
        D --> E[架构关键决策点识别]
        E --> F[架构风险评估]
    ```
    
    ## 2. 架构设计过程
    ```mermaid
    flowchart TD
        A[概念架构设计] --> B[逻辑架构设计]
        B --> C[物理架构设计]
        C --> D[技术架构选型]
        D --> E[接口设计]
        E --> F[数据架构设计]
        F --> G[安全架构设计]
        G --> H[部署架构设计]
    ```
    
    ## 3. 架构验证与优化
    ```mermaid
    flowchart TD
        A[架构原型验证] --> B[性能模型分析]
        B --> C[可扩展性评估]
        C --> D[安全性审查]
        D --> E[可维护性分析]
        E --> F[成本效益评估]
        F --> G[架构优化迭代]
    ```
  </process>

  <guideline>
    # 架构设计指导原则
    
    ## 设计原则
    - **高内聚低耦合**：模块内部功能紧密相关，模块间依赖最小
    - **单一职责**：每个组件只负责一个明确的职责
    - **开放封闭**：对扩展开放，对修改封闭
    - **依赖倒置**：高层模块不依赖低层模块，都依赖抽象
    
    ## 架构模式选择
    - **分层架构**：适用于传统企业应用，结构清晰易维护
    - **微服务架构**：适用于大型复杂系统，支持独立部署和扩展
    - **事件驱动架构**：适用于异步处理和解耦场景
    - **CQRS模式**：适用于读写分离和复杂查询场景
    
    ## 技术选型建议
    - **成熟度优先**：选择经过生产环境验证的技术栈
    - **社区活跃度**：选择有活跃社区支持的技术
    - **团队技能匹配**：考虑团队的技术能力和学习成本
    - **长期维护性**：避免选择过于新颖或小众的技术
  </guideline>

  <rule>
    # 架构设计强制规则
    
    ## 设计文档要求
    1. **架构决策记录**：必须记录重要的架构决策和理由
    2. **接口规范**：所有服务间接口必须有明确的规范定义
    3. **数据模型**：必须定义清晰的数据模型和关系
    4. **部署图**：必须提供详细的部署架构图
    
    ## 质量属性要求
    1. **可用性**：系统可用性不低于99.9%
    2. **性能**：关键接口响应时间不超过500ms
    3. **扩展性**：支持水平扩展到预期规模的3倍
    4. **安全性**：通过安全测试和渗透测试
    
    ## 架构审查要求
    1. **设计评审**：架构设计必须通过技术委员会评审
    2. **原型验证**：关键技术决策必须通过原型验证
    3. **风险评估**：必须识别和评估主要技术风险
    4. **持续监控**：生产环境必须有架构健康度监控
  </rule>

  <constraint>
    # 架构约束条件
    
    ## 技术约束
    - **遗留系统集成**：必须考虑与现有系统的集成方式
    - **技术债务**：平衡新架构与技术债务的处理
    - **团队能力**：架构复杂度不能超出团队承受能力
    - **预算限制**：架构方案必须在预算范围内实现
    
    ## 业务约束
    - **上线时间**：架构实施时间不能影响业务上线计划
    - **业务连续性**：架构升级不能影响现有业务运行
    - **合规要求**：必须满足行业监管和合规要求
    - **数据迁移**：必须有可行的数据迁移方案
    
    ## 运维约束
    - **监控能力**：新架构必须具备完善的监控能力
    - **故障恢复**：必须有快速的故障恢复机制
    - **运维复杂度**：运维复杂度必须在团队承受范围内
    - **自动化程度**：关键流程必须实现自动化
  </constraint>

  <criteria>
    # 架构质量评价标准
    
    ## 架构设计质量
    - ✅ **完整性**：架构设计覆盖所有关键质量属性
    - ✅ **一致性**：架构各层次设计保持一致
    - ✅ **可理解性**：架构设计清晰易懂，便于团队理解
    - ✅ **可验证性**：架构设计可以通过原型或测试验证
    
    ## 非功能质量
    - ✅ **性能表现**：满足或超过性能要求
    - ✅ **可扩展性**：支持业务增长和用户规模扩展
    - ✅ **可维护性**：便于后续功能开发和问题修复
    - ✅ **可靠性**：系统稳定可靠，故障恢复能力强
    
    ## 实施可行性
    - ✅ **技术可行性**：技术方案在当前条件下可实现
    - ✅ **经济可行性**：实施成本在可接受范围内
    - ✅ **时间可行性**：实施时间符合业务要求
    - ✅ **风险可控性**：主要风险已识别并有应对措施
  </criteria>
</execution> 